version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - job-portal-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  frontend:
    build:
      context: ./web-portal
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=
    networks:
      - job-portal-network
    volumes:
      - ./web-portal:/app
      - /app/node_modules
      - /app/.next

  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    networks:
      - job-portal-network
    volumes:
      - .:/app
      - /app/__pycache__

networks:
  job-portal-network:
    driver: bridge
