import time
import logging
import random
import uuid
from typing import Dict, List
from dataclasses import dataclass, asdict
from urllib.parse import quote_plus
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, ElementClickInterceptedException
from selenium.webdriver.common.action_chains import ActionChains
from fastapi import FastAPI, HTTPException, Body
from fastapi.responses import JSONResponse
import uvicorn
import undetected_chromedriver as uc
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper", version="3.0")

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "https://ffb46ce19203.ngrok-free.app",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@dataclass
class JobData:
    """Data structure for job information"""
    job_id: str = ""
    title: str = ""
    company_name: str = ""
    location: str = ""
    experience: str = ""
    salary: str = ""
    job_description: str = ""
    skills: List[str] = None
    posted_date: str = ""
    job_url: str = ""
    company_description: str = ""
    roles: str = ""
    industry: str = ""
    function: str = ""
    
    def __post_init__(self):
        if self.skills is None:
            self.skills = []

class FounditScraper:
    """Foundit scraper with direct URL navigation and click-through job extraction"""
    
    def __init__(self, headless: bool = True, timeout: int = 60):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
        # Selectors for job cards and job detail pages
        self.selectors = {
            'cookie_banner': '#cookieBanner',
            'accept_cookies': '#acceptAll',
            'job_cards': 'div[data-index]',  # Job cards with data-index
            'job_title': 'h3.text-darkKnight-700.line-clamp-2.text-ellipsis.text-base.font-bold[title]',
            'company_name': 'span p, .text-sm.font-normal p',
            'experience_label': 'label',  # Experience labels
            'location_label': 'label',  # Location labels  
            'posted_date': 'label.text-xxs',  # Posted date labels
            'salary_label': 'label',  # Salary labels
            'apply_button': '#applyBtn, button:contains("Apply")',
            'save_button': 'button:contains("Save")',
            'company_logo': 'img[alt]',
            'next_page': '.pagination-next a, .next-page a, button:contains("Next")',
            # Job detail page selectors (updated based on actual page structure from job-page.md)
            'job_detail_title': 'h1.text-content-primary.line-clamp-2.text-lg.font-bold, h1.text-2xl, h1',
            'job_detail_company': 'a[href*="jobs-career"], a[target="_blank"], .text-content-secondary a',
            'job_detail_description': '#jobDescription .job-description-content .break-words, #jobDescription .break-words, .job-description-content',
            'job_detail_skills': '#skillSectionNew .bg-surface-secondary a, .bg-surface-secondary a, .skill-tag a',
            'job_detail_posted_date': '.text-content-tertiary:contains("Date Posted:")',
            'job_detail_job_id': '.text-content-tertiary:contains("Job ID:")',
            'job_detail_role': 'p:contains("Role:") span a, .text-content-primary:contains("Role:") + .text-content-secondary a',
            'job_detail_industry': 'p:contains("Industry:") span a, .text-content-primary:contains("Industry:") + .text-content-secondary a',
            'job_detail_function': 'p:contains("Function:") span a, .text-content-primary:contains("Function:") + .text-content-secondary a',
            'job_detail_experience': '.flex.items-center.gap-2:has(svg) span:contains("Years")',
            'job_detail_location': '.flex.items-center.gap-2:has(svg) a[href*="jobs-in-"]'
        }
    
    def setup_driver(self) -> uc.Chrome:
        """Setup Chrome driver with optimized options"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                options = uc.ChromeOptions()
                
                # Essential options for scraping
                if self.headless:
                    options.add_argument('--headless=new')
                    options.add_argument('--window-size=1920,1080')
                
                # Anti-detection options
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                
                # Performance options
                options.add_argument('--disable-images')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-plugins')
                options.add_argument('--disable-gpu')
                options.add_argument('--disable-web-security')
                options.add_argument('--disable-features=VizDisplayCompositor')
                options.add_argument('--disable-logging')
                options.add_argument('--disable-dev-tools')
                options.add_argument('--no-first-run')
                options.add_argument('--no-default-browser-check')
                options.add_argument('--disable-default-apps')
                options.add_argument('--disable-popup-blocking')
                options.add_argument('--ignore-certificate-errors')
                options.add_argument('--ignore-ssl-errors')
                options.add_argument('--ignore-certificate-errors-spki-list')
                
                # User agent rotation
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
                options.add_argument(f'--user-agent={random.choice(user_agents)}')
                
                logger.info(f"Attempting to initialize Chrome driver (attempt {attempt + 1}/{max_retries})")
                
                self.driver = uc.Chrome(options=options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.wait = WebDriverWait(self.driver, self.timeout)
                
                logger.info("Chrome driver initialized successfully")
                return self.driver
                
            except Exception as e:
                logger.error(f"Failed to setup driver (attempt {attempt + 1}/{max_retries}): {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                if attempt == max_retries - 1:
                    raise WebDriverException(f"Driver setup failed after {max_retries} attempts: {e}")
                
                # Wait before retry
                time.sleep(2)
        
        raise WebDriverException("Failed to initialize Chrome driver")
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    def _navigate_method_1_job_title(self, card_element) -> bool:
        """Method 1: Click on the job title (most common approach)"""
        try:
            # Look for the job title which is usually clickable
            job_title = card_element.find_element(By.CSS_SELECTOR, self.selectors['job_title'])
            job_title.click()
            return True
        except Exception as e:
            logger.debug(f"Method 1 (job title click) failed: {e}")
            return False

    def _navigate_method_2_action_chains(self, card_element) -> bool:
        """Method 2: Click anywhere on the card using ActionChains"""
        try:
            # The card div often has cursor-pointer class indicating it's clickable
            ActionChains(self.driver).click(card_element).perform()
            return True
        except Exception as e:
            logger.debug(f"Method 2 (ActionChains click) failed: {e}")
            return False

    def _navigate_method_3_clickable_elements(self, card_element) -> bool:
        """Method 3: Look for specific clickable elements within the card"""
        try:
            # Try clicking on various clickable elements
            clickable_selectors = [
                'a', '[role="button"]', '.cursor-pointer', 'h3', '[onclick]',
                'span p', '.company-name', '.job-title'
            ]

            for selector in clickable_selectors:
                try:
                    elements = card_element.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        elements[0].click()
                        return True
                except:
                    continue
            return False
        except Exception as e:
            logger.debug(f"Method 3 (clickable elements) failed: {e}")
            return False

    def _navigate_method_4_javascript(self, card_element) -> bool:
        """Method 4: Use JavaScript to find and trigger click events with debugging"""
        try:
            # Enhanced JavaScript with debugging capabilities
            script = """
            var card = arguments[0];

            // Initialize debugging
            var debugInfo = {
                attempts: [],
                jobData: {},
                navigation: {},
                success: false
            };

            // Strategy 1: Check for job data in card attributes and data
            try {
                var dataAttrs = {};
                for (var i = 0; i < card.attributes.length; i++) {
                    var attr = card.attributes[i];
                    if (attr.name.startsWith('data-')) {
                        dataAttrs[attr.name] = attr.value;
                    }
                }
                debugInfo.jobData.attributes = dataAttrs;

                // Check for job ID patterns in text content
                var cardText = card.textContent;
                var numberPatterns = cardText.match(/\\b\\d{7,10}\\b/g);
                if (numberPatterns) {
                    debugInfo.jobData.potentialIds = numberPatterns;
                }

                debugInfo.attempts.push('Job data extraction completed');
            } catch (e) {
                debugInfo.attempts.push('Job data extraction failed: ' + e.message);
            }

            // Strategy 2: Monitor for network requests during click
            var originalXHR = window.XMLHttpRequest;
            var networkCalls = [];

            window.XMLHttpRequest = function() {
                var xhr = new originalXHR();
                var originalOpen = xhr.open;

                xhr.open = function(method, url, ...args) {
                    if (url.includes('job') || url.includes('apply') || url.includes('detail')) {
                        networkCalls.push({method: method, url: url});
                    }
                    return originalOpen.apply(this, [method, url, ...args]);
                };

                return xhr;
            };

            // Strategy 3: Monitor URL changes
            var originalHref = window.location.href;

            // Strategy 4: Try clicking with comprehensive event simulation
            var clickMethods = [
                // Method 1: Direct click
                function() {
                    card.click();
                    return 'Direct card click';
                },
                // Method 2: Click on specific child elements
                function() {
                    var clickableElements = card.querySelectorAll('h3, .cursor-pointer, a, [onclick]');
                    for (var i = 0; i < clickableElements.length; i++) {
                        try {
                            clickableElements[i].click();
                            return 'Clicked child element: ' + clickableElements[i].tagName + '.' + clickableElements[i].className;
                        } catch (e) {
                            continue;
                        }
                    }
                    return 'No clickable children found';
                },
                // Method 3: Comprehensive mouse event simulation
                function() {
                    var rect = card.getBoundingClientRect();
                    var events = ['mousedown', 'mouseup', 'click'];

                    for (var j = 0; j < events.length; j++) {
                        var event = new MouseEvent(events[j], {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: rect.left + rect.width / 2,
                            clientY: rect.top + rect.height / 2
                        });
                        card.dispatchEvent(event);
                    }
                    return 'Mouse events dispatched';
                },
                // Method 4: Try to find and execute navigation function
                function() {
                    // Look for navigation functions in window object
                    var navFunctions = ['navigateToJob', 'showJobDetail', 'openJob', 'viewJob'];
                    for (var k = 0; k < navFunctions.length; k++) {
                        if (window[navFunctions[k]] && typeof window[navFunctions[k]] === 'function') {
                            try {
                                var cardIndex = card.getAttribute('data-index');
                                window[navFunctions[k]](cardIndex);
                                return 'Called ' + navFunctions[k] + ' with index ' + cardIndex;
                            } catch (e) {
                                continue;
                            }
                        }
                    }
                    return 'No navigation functions found';
                }
            ];

            // Execute click methods
            for (var m = 0; m < clickMethods.length; m++) {
                try {
                    var result = clickMethods[m]();
                    debugInfo.attempts.push('Method ' + (m + 1) + ': ' + result);

                    // Check for navigation after each method
                    setTimeout(function() {
                        if (window.location.href !== originalHref) {
                            debugInfo.navigation.detected = true;
                            debugInfo.navigation.newUrl = window.location.href;
                            debugInfo.success = true;
                        }
                    }, 100);

                } catch (e) {
                    debugInfo.attempts.push('Method ' + (m + 1) + ' failed: ' + e.message);
                }
            }

            // Restore original XMLHttpRequest
            window.XMLHttpRequest = originalXHR;

            // Add network calls to debug info
            debugInfo.networkCalls = networkCalls;

            return debugInfo;
            """

            result = self.driver.execute_script(script, card_element)

            # Log detailed debug information
            logger.info(f"JavaScript debug info: {result}")

            if result.get('jobData', {}).get('potentialIds'):
                logger.info(f"Potential job IDs found: {result['jobData']['potentialIds']}")

            if result.get('networkCalls'):
                logger.info(f"Network calls detected: {result['networkCalls']}")

            return result.get('success', False)

        except Exception as e:
            logger.debug(f"Method 4 (Enhanced JavaScript) failed: {e}")
            return False

    def generate_pseudo_job_url(self, job_data: JobData, card_element=None) -> str:
        """Generate a pseudo job URL when direct navigation fails"""
        try:
            import re
            import hashlib

            # Clean job title and company for URL
            clean_title = re.sub(r'[^a-zA-Z0-9\s]', '', job_data.title).lower().replace(' ', '-')
            clean_company = re.sub(r'[^a-zA-Z0-9\s]', '', job_data.company_name).lower().replace(' ', '-')

            # Try to extract any identifier from the card element
            job_id = None
            if card_element:
                try:
                    job_id_attrs = ['data-index', 'data-job-id', 'data-id', 'id']
                    for attr in job_id_attrs:
                        attr_value = card_element.get_attribute(attr)
                        if attr_value and attr_value.strip():
                            job_id = attr_value.strip()
                            break
                except Exception:
                    pass

            # Generate a unique identifier if none found
            if not job_id:
                # Create hash from job data
                job_hash = hashlib.md5(f"{job_data.title}{job_data.company_name}{job_data.location}".encode()).hexdigest()[:8]
                job_id = job_hash

            # Create pseudo URL in foundit format
            pseudo_url = f"https://www.foundit.in/job/{clean_title}-{clean_company}-{job_id}"

            return pseudo_url

        except Exception as e:
            logger.debug(f"Error generating pseudo URL: {e}")
            return "https://www.foundit.in/job/not-available"

    def _navigate_back_to_search_results(self, search_url: str) -> bool:
        """Navigate back to search results page using multiple methods"""
        try:
            # Method 1: Use browser back button (faster and more reliable)
            logger.debug("Trying back button navigation...")
            self.driver.back()
            self.random_delay(2, 3)

            # Check if we're back on search results
            current_url = self.driver.current_url
            if '/search/' in current_url:
                # Wait for job cards to load
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
                    )
                    logger.info("✓ Successfully returned to search results using back button")
                    return True
                except TimeoutException:
                    logger.debug("Back button navigation succeeded but job cards didn't load")
            else:
                logger.debug("Back button didn't return to search results")

        except Exception as e:
            logger.debug(f"Back button navigation failed: {e}")

        # Method 2: Direct navigation to search URL (fallback)
        try:
            logger.debug("Using direct navigation to search URL...")
            self.driver.get(search_url)
            self.random_delay(3, 5)

            # Wait for search results to reload
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
            )
            logger.info("✓ Successfully returned to search results using direct navigation")
            return True

        except TimeoutException:
            logger.warning("Search results page did not reload properly after direct navigation")
        except Exception as e:
            logger.error(f"Direct navigation failed: {e}")

        logger.error("Failed to return to search results page")
        return False

    def _find_job_card_safely(self, card_index: int, expected_title: str = None):
        """Safely find job card by index with title verification"""
        try:
            # Wait for job cards to be present
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
            )

            # Find all job cards
            all_cards = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['job_cards'])
            if card_index >= len(all_cards):
                logger.warning(f"Job card index {card_index} not found (found {len(all_cards)} cards)")
                return None

            card = all_cards[card_index]

            # Verify this is the right job by checking the title (if provided)
            if expected_title:
                try:
                    card_title_elem = card.find_element(By.CSS_SELECTOR, self.selectors['job_title'])
                    card_title = card_title_elem.get_attribute('title') or card_title_elem.text.strip()

                    if card_title != expected_title:
                        logger.warning(f"Job card title mismatch. Expected: {expected_title}, Found: {card_title}")

                        # Try to find the correct card by title
                        for i, check_card in enumerate(all_cards):
                            try:
                                check_title_elem = check_card.find_element(By.CSS_SELECTOR, self.selectors['job_title'])
                                check_title = check_title_elem.get_attribute('title') or check_title_elem.text.strip()
                                if check_title == expected_title:
                                    logger.info(f"Found matching job card at index {i}")
                                    return check_card
                            except:
                                continue

                        # If we couldn't find by title, use the original index
                        logger.warning(f"Could not find card by title, using original index {card_index}")

                except Exception as e:
                    logger.debug(f"Could not verify job card title: {e}")

            return card

        except TimeoutException:
            logger.warning("Job cards not found on page")
            return None
        except Exception as e:
            logger.error(f"Error finding job card safely: {e}")
            return None

    def _extract_job_id_from_page_data(self, card_element, job_title: str) -> str:
        """Extract job ID using comprehensive debugging strategies"""
        try:
            # Execute comprehensive job ID extraction script
            script = """
            var card = arguments[0];
            var jobTitle = arguments[1];
            var extractionResults = {
                methods: [],
                jobIds: [],
                success: false
            };

            // Strategy 1: Check card and parent elements for data attributes
            try {
                var cardJobIds = [];
                var currentElement = card;
                var depth = 0;

                while (currentElement && depth < 5) {
                    // Check all attributes
                    for (var j = 0; j < currentElement.attributes.length; j++) {
                        var attr = currentElement.attributes[j];
                        if (attr.name.includes('job') || attr.name.includes('id')) {
                            var value = attr.value;
                            if (/\\d{7,10}/.test(value)) {
                                cardJobIds.push(value);
                            }
                        }
                    }

                    // Check data attributes specifically
                    if (currentElement.dataset) {
                        for (var dataKey in currentElement.dataset) {
                            var dataValue = currentElement.dataset[dataKey];
                            if (/\\d{7,10}/.test(dataValue)) {
                                cardJobIds.push(dataValue);
                            }
                        }
                    }

                    currentElement = currentElement.parentElement;
                    depth++;
                }

                extractionResults.methods.push('Card hierarchy scan: ' + cardJobIds.length + ' matches');
                extractionResults.jobIds.push(...cardJobIds);
            } catch (e) {
                extractionResults.methods.push('Card scan failed: ' + e.message);
            }

            // Strategy 2: Check script tags for embedded job data
            try {
                var scriptJobIds = [];
                var scripts = document.querySelectorAll('script');
                for (var i = 0; i < scripts.length; i++) {
                    var scriptContent = scripts[i].textContent;
                    if (scriptContent.includes('job') && scriptContent.includes(jobTitle)) {
                        // Look for job ID patterns
                        var jobIdMatches = scriptContent.match(/(?:jobId|job_id|id)["']?\\s*[:=]\\s*["']?(\\d{7,10})/gi);
                        if (jobIdMatches) {
                            scriptJobIds.push(...jobIdMatches.map(match => match.match(/\\d{7,10}/)[0]));
                        }
                    }
                }
                extractionResults.methods.push('Script tags scan: ' + scriptJobIds.length + ' matches');
                extractionResults.jobIds.push(...scriptJobIds);
            } catch (e) {
                extractionResults.methods.push('Script scan failed: ' + e.message);
            }

            // Strategy 3: Check for number patterns in card text
            try {
                var textJobIds = [];
                var cardText = card.textContent;
                var numberPatterns = cardText.match(/\\b\\d{7,10}\\b/g);
                if (numberPatterns) {
                    textJobIds.push(...numberPatterns);
                }

                extractionResults.methods.push('Text pattern scan: ' + textJobIds.length + ' matches');
                extractionResults.jobIds.push(...textJobIds);
            } catch (e) {
                extractionResults.methods.push('Text scan failed: ' + e.message);
            }

            // Remove duplicates and filter valid job IDs
            extractionResults.jobIds = [...new Set(extractionResults.jobIds)];
            extractionResults.success = extractionResults.jobIds.length > 0;

            return extractionResults;
            """

            result = self.driver.execute_script(script, card_element, job_title)

            # Log extraction results
            logger.info(f"Job ID extraction methods: {result.get('methods', [])}")

            if result.get('jobIds'):
                logger.info(f"Potential job IDs found: {result['jobIds']}")
                # Return the first valid-looking job ID
                for job_id in result['jobIds']:
                    if isinstance(job_id, str) and len(job_id) >= 7:
                        return job_id

            return None

        except Exception as e:
            logger.debug(f"Job ID extraction failed: {e}")
            return None
    
    def create_search_url(self, job_title: str, location: str) -> str:
        """Create direct search URL for foundit.in"""
        try:
            # Generate random UUIDs for search parameters
            search_id = str(uuid.uuid4())
            child_search_id = str(uuid.uuid4())
            
            # URL encode the parameters
            encoded_job_title = quote_plus(job_title)
            encoded_location = quote_plus(location)
            
            # Create URL slug for the path (lowercase, replace spaces with hyphens)
            job_slug = job_title.lower().replace(' ', '-').replace('+', '-')
            location_slug = location.lower().replace(' ', '-').replace('/', '-')
            
            # Construct the search URL
            search_url = (
                f"https://www.foundit.in/search/{job_slug}-jobs-in-{location_slug}"
                f"?query={encoded_job_title}"
                f"&locations={encoded_location}"
                f"&queryDerived=true"
                f"&searchId={search_id}"
                f"&child_search_id={child_search_id}"
            )
            
            logger.info(f"Created search URL: {search_url}")
            return search_url
            
        except Exception as e:
            logger.error(f"Error creating search URL: {e}")
            # Fallback to basic URL
            return f"https://www.foundit.in/search/{job_title.lower().replace(' ', '-')}-jobs"
    
    def navigate_to_search_results(self, job_title: str, location: str) -> bool:
        """Navigate directly to search results page"""
        try:
            search_url = self.create_search_url(job_title, location)
            logger.info(f"Navigating to search results: {search_url}")
            
            self.driver.get(search_url)
            self.random_delay(2, 4)
            
            # Handle cookies if present
            self.handle_cookies()
            
            # Wait for job cards to load
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
                )
                logger.info("Search results page loaded successfully")
                return True
            except TimeoutException:
                # Check if it's a no results page
                page_title = self.driver.title
                if ': 0 ' in page_title or 'no jobs' in page_title.lower():
                    logger.info("No jobs found for the search criteria")
                    return True
                else:
                    logger.warning("Search results page did not load properly")
                    return False
                    
        except Exception as e:
            logger.error(f"Error navigating to search results: {e}")
            return False
    
    def handle_cookies(self):
        """Handle cookie consent banner if present"""
        try:
            # Check if cookie banner exists
            cookie_banner = self.driver.find_element(By.CSS_SELECTOR, self.selectors['cookie_banner'])
            if cookie_banner.is_displayed():
                logger.info("Cookie banner found, accepting cookies...")
                accept_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors['accept_cookies'])
                accept_button.click()
                self.random_delay(1, 2)
                logger.info("Cookies accepted successfully")
        except NoSuchElementException:
            logger.info("No cookie banner found")
        except Exception as e:
            logger.warning(f"Error handling cookies: {e}")

    def extract_job_cards_data(self) -> List[JobData]:
        """Extract basic job data from search results page"""
        jobs = []
        try:
            # Find all job cards
            job_cards = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['job_cards'])
            logger.info(f"Found {len(job_cards)} job cards on current page")

            for index, card in enumerate(job_cards):
                try:
                    job = JobData()
                    job.job_id = str(index + 1)  # Use index as temporary ID

                    # Extract job title
                    try:
                        title_elem = card.find_element(By.CSS_SELECTOR, self.selectors['job_title'])
                        job.title = title_elem.get_attribute('title') or title_elem.text.strip()
                    except NoSuchElementException:
                        continue  # Skip if no title found

                    # Extract company name
                    try:
                        company_elem = card.find_element(By.CSS_SELECTOR, self.selectors['company_name'])
                        job.company_name = company_elem.text.strip()
                    except NoSuchElementException:
                        pass

                    # Extract experience, location, salary, posted date from labels
                    labels = card.find_elements(By.TAG_NAME, 'label')
                    for label in labels:
                        text = label.text.strip()
                        if 'yrs' in text.lower() or 'year' in text.lower():
                            job.experience = text
                        elif any(city in text.lower() for city in ['bengaluru', 'bangalore', 'mumbai', 'delhi', 'chennai', 'hyderabad', 'pune', 'gurgaon', 'noida', 'kolkata', 'ahmedabad', 'jaipur', 'lucknow', 'kanpur', 'nagpur', 'indore', 'thane', 'bhopal', 'visakhapatnam', 'pimpri', 'patna', 'vadodara', 'ludhiana', 'agra', 'nashik', 'faridabad', 'meerut', 'rajkot', 'kalyan', 'vasai', 'varanasi', 'srinagar', 'aurangabad', 'dhanbad', 'amritsar', 'navi mumbai', 'allahabad', 'ranchi', 'howrah', 'coimbatore', 'jabalpur', 'gwalior', 'vijayawada', 'jodhpur', 'madurai', 'raipur', 'kota', 'guwahati', 'chandigarh', 'solapur', 'hubli', 'tiruchirappalli', 'bareilly', 'mysore', 'tiruppur', 'gurgaon', 'aligarh', 'jalandhar', 'bhubaneswar', 'salem', 'warangal', 'guntur', 'bhiwandi', 'saharanpur', 'gorakhpur', 'bikaner', 'amravati', 'noida', 'jamshedpur', 'bhilai', 'cuttack', 'firozabad', 'kochi', 'nellore', 'bhavnagar', 'dehradun', 'durgapur', 'asansol', 'rourkela', 'nanded', 'kolhapur', 'ajmer', 'akola', 'gulbarga', 'jamnagar', 'ujjain', 'loni', 'siliguri', 'jhansi', 'ulhasnagar', 'jammu', 'sangli-miraj & kupwad', 'mangalore', 'erode', 'belgaum', 'ambattur', 'tirunelveli', 'malegaon', 'gaya', 'jalgaon', 'udaipur', 'maheshtala']):
                            job.location = text
                        elif '₹' in text or 'lpa' in text.lower() or 'salary' in text.lower():
                            job.salary = text
                        elif 'posted' in text.lower() or 'ago' in text.lower():
                            job.posted_date = text.replace('Posted', '').strip()

                    # Extract company logo
                    try:
                        logo_elem = card.find_element(By.CSS_SELECTOR, self.selectors['company_logo'])
                        job.company_description = f"Logo: {logo_elem.get_attribute('src')}"
                    except NoSuchElementException:
                        pass

                    # Set current page URL as job URL (will be updated when clicking through)
                    job.job_url = self.driver.current_url

                    if job.title and job.company_name:
                        jobs.append(job)
                        logger.debug(f"Extracted basic job data: {job.title} at {job.company_name}")

                except Exception as e:
                    logger.warning(f"Error extracting data from job card {index}: {e}")
                    continue

            logger.info(f"Successfully extracted {len(jobs)} jobs from search results")
            return jobs

        except Exception as e:
            logger.error(f"Error extracting job cards data: {e}")
            return []

    def click_through_to_job_detail(self, job_data: JobData, card_index: int) -> JobData:
        """Click on job card to navigate to detailed job page"""
        try:
            # Store current URL to return if needed
            search_url = self.driver.current_url
            logger.info(f"Attempting to click through for job: {job_data.title} (index: {card_index})")

            # Re-find job cards to avoid stale element references (improved approach)
            card = self._find_job_card_safely(card_index, job_data.title)
            if not card:
                logger.warning(f"Could not find job card for index {card_index}")
                return job_data

            # Simplified and more targeted click strategies
            clicked = False

            # First, try to scroll the card into view and ensure it's clickable
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", card)
            self.random_delay(1, 2)

            # Try multiple navigation methods based on the provided code
            navigation_methods = [
                self._navigate_method_1_job_title,
                self._navigate_method_2_action_chains,
                self._navigate_method_3_clickable_elements,
                self._navigate_method_4_javascript
            ]

            for method_num, method in enumerate(navigation_methods, 1):
                try:
                    logger.info(f"Trying navigation method {method_num}...")

                    if method(card):
                        # Wait and check for navigation
                        self.random_delay(2, 3)
                        current_url = self.driver.current_url

                        # Check if we navigated to a job detail page
                        if current_url != search_url:
                            job_detail_indicators = ['/job/', '/jobs/', '/job-detail', '/vacancy', '/position']
                            is_job_detail = any(indicator in current_url.lower() for indicator in job_detail_indicators)

                            if is_job_detail:
                                logger.info(f"✓ Successfully navigated to job detail page: {current_url}")
                                job_data.job_url = current_url
                                clicked = True
                                break
                            else:
                                logger.debug(f"URL changed but not to job detail: {current_url}")
                                # Navigate back to search results
                                self.driver.get(search_url)
                                self.random_delay(2, 3)
                        else:
                            logger.debug(f"No navigation detected with method {method_num}")

                except Exception as e:
                    logger.debug(f"Navigation method {method_num} failed: {e}")
                    continue

            # If no click strategy worked, try to extract job ID and construct URL
            if not clicked:
                logger.warning(f"All click strategies failed for job: {job_data.title}")
                logger.info("Attempting to extract job ID from page data...")

                # Try to extract job ID using debugging strategies
                extracted_job_id = self._extract_job_id_from_page_data(card, job_data.title)

                if extracted_job_id:
                    logger.info(f"Extracted job ID: {extracted_job_id}")
                    # Construct proper job URL using extracted ID
                    constructed_url = f"https://www.foundit.in/job/{job_data.title.lower().replace(' ', '-')}-{job_data.company_name.lower().replace(' ', '-')}-{extracted_job_id}"

                    # Try navigating to the constructed URL
                    try:
                        logger.info(f"Trying constructed URL: {constructed_url}")
                        self.driver.get(constructed_url)
                        self.random_delay(3, 5)

                        current_url = self.driver.current_url
                        if '/job/' in current_url and 'error' not in current_url.lower():
                            logger.info(f"✓ Successfully navigated to constructed URL: {current_url}")
                            job_data.job_url = current_url
                            clicked = True
                        else:
                            logger.debug(f"Constructed URL failed: {current_url}")
                            # Navigate back to search results
                            self.driver.get(search_url)
                            self.random_delay(2, 3)
                    except Exception as e:
                        logger.debug(f"Error with constructed URL: {e}")
                        # Navigate back to search results
                        try:
                            self.driver.get(search_url)
                            self.random_delay(2, 3)
                        except:
                            pass

                if not clicked:
                    logger.info("Creating fallback job URL based on available data")

            if clicked:
                # Extract detailed information from job detail page
                logger.info(f"Extracting detailed job information for: {job_data.title}")
                job_data = self.extract_job_detail_data(job_data)

                # Navigate back to search results for next job
                logger.info("Navigating back to search results")

                # Use the more reliable back navigation approach
                self._navigate_back_to_search_results(search_url)
            else:
                # If all navigation attempts failed, create a fallback URL
                logger.warning(f"Could not click through to job detail for: {job_data.title}")
                logger.info("Creating fallback job URL based on available data")

                # Create a pseudo job URL using the helper method
                try:
                    fallback_url = self.generate_pseudo_job_url(job_data, card)
                    job_data.job_url = fallback_url
                    logger.info(f"Created fallback job URL: {fallback_url}")

                except Exception as e:
                    logger.debug(f"Error creating fallback URL: {e}")
                    # Use search URL as absolute fallback
                    job_data.job_url = search_url

            return job_data

        except Exception as e:
            logger.error(f"Error in click-through navigation: {e}")
            # Ensure we're back on search results page
            try:
                if self.driver.current_url != search_url:
                    self.driver.get(search_url)
                    self.random_delay(2, 3)
            except:
                pass
            return job_data

    def _try_keyboard_navigation(self, element):
        """Try keyboard navigation (Tab + Enter) to activate element"""
        try:
            from selenium.webdriver.common.keys import Keys
            element.send_keys(Keys.TAB)
            self.random_delay(0.5, 1)
            element.send_keys(Keys.ENTER)
            logger.debug("Keyboard navigation attempted")
        except Exception as e:
            logger.debug(f"Keyboard navigation failed: {e}")
            raise e

    def _try_double_click(self, element):
        """Try double-click on element"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.double_click(element).perform()
            logger.debug("Double-click attempted")
        except Exception as e:
            logger.debug(f"Double-click failed: {e}")
            raise e

    def _try_programmatic_navigation(self, element, card_index):
        """Try to navigate programmatically by constructing job URLs"""
        try:
            # Try to construct job URL based on common patterns
            current_url = self.driver.current_url
            base_url = "https://www.foundit.in"

            # Method 1: Try to find job ID in the card's HTML
            card_html = element.get_attribute('outerHTML')
            import re

            # Look for job IDs in various formats
            job_id_patterns = [
                r'data-job-id="([^"]+)"',
                r'data-id="([^"]+)"',
                r'jobId["\']?\s*:\s*["\']?([^"\']+)',
                r'id["\']?\s*:\s*["\']?([^"\']+)',
                r'/job/([^/\s"\']+)',
                r'jid=([^&\s"\']+)'
            ]

            for pattern in job_id_patterns:
                matches = re.findall(pattern, card_html, re.IGNORECASE)
                if matches:
                    job_id = matches[0]
                    # Try different URL patterns
                    potential_urls = [
                        f"{base_url}/job/{job_id}",
                        f"{base_url}/jobs/{job_id}",
                        f"{base_url}/job-detail/{job_id}",
                        f"{base_url}/vacancy/{job_id}",
                        f"{current_url}&jobId={job_id}",
                        f"{current_url}#{job_id}"
                    ]

                    for url in potential_urls:
                        try:
                            logger.debug(f"Trying programmatic URL: {url}")
                            self.driver.get(url)
                            self.random_delay(2, 3)
                            if self.driver.current_url != current_url and 'error' not in self.driver.current_url.lower():
                                logger.info(f"Programmatic navigation successful: {self.driver.current_url}")
                                return
                        except Exception:
                            continue

            # Method 2: Try to execute any JavaScript navigation functions
            navigation_scripts = [
                f"if (window.navigateToJob) window.navigateToJob({card_index});",
                f"if (window.showJobDetail) window.showJobDetail({card_index});",
                f"if (window.openJob) window.openJob({card_index});",
                "var cards = document.querySelectorAll('[data-index]'); if (cards[" + str(card_index) + "] && cards[" + str(card_index) + "].click) cards[" + str(card_index) + "].click();"
            ]

            for script in navigation_scripts:
                try:
                    self.driver.execute_script(script)
                    self.random_delay(1, 2)
                    if self.driver.current_url != current_url:
                        logger.info(f"JavaScript navigation successful: {self.driver.current_url}")
                        return
                except Exception:
                    continue

            logger.debug("Programmatic navigation failed")
            raise Exception("No programmatic navigation method worked")

        except Exception as e:
            logger.debug(f"Programmatic navigation failed: {e}")
            raise e

    def extract_job_detail_data(self, job_data: JobData) -> JobData:
        """Extract detailed job information from job detail page"""
        try:
            current_url = self.driver.current_url
            logger.info(f"Extracting job details from: {current_url}")

            # Wait for page to load - try multiple selectors
            page_loaded = False
            load_selectors = [
                '#jobDetailContainer',
                '.job-description-content',
                '#jobDescription',
                'h1',
                '[data-testid="job-title"]',
                '.job-title',
                'main',
                'article'
            ]

            for selector in load_selectors:
                try:
                    WebDriverWait(self.driver, 8).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"Page loaded, found element: {selector}")
                    page_loaded = True
                    break
                except TimeoutException:
                    continue

            if not page_loaded:
                logger.warning("Job detail page did not load properly, using basic extraction")

            # Extract job title (try multiple selectors based on job-page.md structure)
            title_selectors = [
                'h1.text-content-primary.line-clamp-2.text-lg.font-bold.leading-\\[26px\\].md\\:\\!text-2xl.md\\:\\!leading-9',
                'h1.text-content-primary',
                'h1.text-lg.font-bold',
                'h1',
                '[data-testid="job-title"]',
                '.job-title'
            ]

            for selector in title_selectors:
                try:
                    title_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if title_elem.text.strip():
                        job_data.title = title_elem.text.strip()
                        logger.debug(f"Found title with selector {selector}: {job_data.title}")
                        break
                except NoSuchElementException:
                    continue

            # Extract company name (try multiple selectors based on job-page.md structure)
            company_selectors = [
                'a[href*="jobs-career"][target="_blank"].line-clamp-2.text-sm.font-normal.underline',
                'a[href*="jobs-career"]',
                'a[target="_blank"]',
                '.text-content-secondary a',
                '.company-name',
                '[data-testid="company-name"]',
                'a[href*="company"]'
            ]

            for selector in company_selectors:
                try:
                    company_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if company_elem.text.strip():
                        job_data.company_name = company_elem.text.strip()
                        logger.debug(f"Found company with selector {selector}: {job_data.company_name}")
                        break
                except NoSuchElementException:
                    continue

            # Extract job description (try multiple selectors based on job-page.md structure)
            description_selectors = [
                '#jobDescription .job-description-content .break-words.text-\\[13px\\].leading-6',
                '#jobDescription .job-description-content .break-words',
                '#jobDescription .break-words',
                '.job-description-content .break-words',
                '#jobDescription',
                '.job-description-content',
                '.job-description',
                '.description',
                '.break-words',
                '[data-testid="job-description"]'
            ]

            for selector in description_selectors:
                try:
                    desc_elem = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if desc_elem.text.strip():
                        job_data.job_description = desc_elem.text.strip()
                        logger.debug(f"Found description with selector {selector}")
                        break
                except NoSuchElementException:
                    continue

            # Extract skills (try multiple selectors based on job-page.md structure)
            skill_selectors = [
                '#skillSectionNew .bg-surface-secondary a.text-xs.font-normal.leading-4',
                '#skillSectionNew .bg-surface-secondary a',
                '.bg-surface-secondary a',
                '#skillSectionNew label',
                '.bg-surface-secondary label',
                '.skills label',
                '.skill-tag',
                '[data-testid="skills"] span'
            ]

            for selector in skill_selectors:
                try:
                    skill_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if skill_elements:
                        skills = []
                        for skill_elem in skill_elements:
                            skill_text = skill_elem.text.strip()
                            if skill_text and skill_text not in skills and len(skill_text) < 50:
                                skills.append(skill_text)
                        if skills:
                            job_data.skills = skills
                            logger.debug(f"Found {len(skills)} skills with selector {selector}")
                            break
                except NoSuchElementException:
                    continue

            # Extract job metadata (ID, posted date, etc.) based on job-page.md structure
            try:
                # Extract Job ID
                job_id_elem = self.driver.find_element(By.XPATH, "//p[contains(text(), 'Job ID:')]")
                job_id = job_id_elem.text.replace('Job ID:', '').strip()
                if job_id:
                    job_data.job_id = job_id
                    logger.debug(f"Found job_id: {job_id}")
            except NoSuchElementException:
                pass

            try:
                # Extract Posted Date
                posted_elem = self.driver.find_element(By.XPATH, "//p[contains(text(), 'Date Posted:')]")
                posted_date = posted_elem.text.replace('Date Posted:', '').strip()
                if posted_date:
                    job_data.posted_date = posted_date
                    logger.debug(f"Found posted_date: {posted_date}")
            except NoSuchElementException:
                pass

            # Extract More Info section data
            more_info_patterns = [
                ("Role:", "roles"),
                ("Industry:", "industry"),
                ("Function:", "function"),
                ("Job Type:", "job_type")
            ]

            for pattern, field in more_info_patterns:
                try:
                    # Look for the pattern in the More Info section
                    elem = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{pattern}')]/following-sibling::span/a")
                    value = elem.text.strip()
                    if value:
                        setattr(job_data, field, value)
                        logger.debug(f"Found {field}: {value}")
                except NoSuchElementException:
                    continue

            # Extract salary information
            salary_selectors = [
                "//span[contains(text(), '₹') or contains(text(), 'LPA') or contains(text(), 'salary')]",
                "//label[contains(text(), '₹') or contains(text(), 'LPA')]",
                ".salary",
                "[data-testid='salary']"
            ]

            for selector in salary_selectors:
                try:
                    if selector.startswith('//'):
                        salary_elem = self.driver.find_element(By.XPATH, selector)
                    else:
                        salary_elem = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if salary_elem.text.strip():
                        job_data.salary = salary_elem.text.strip()
                        logger.debug(f"Found salary: {job_data.salary}")
                        break
                except NoSuchElementException:
                    continue

            # Extract additional experience and location if not already present (based on job-page.md structure)
            if not job_data.experience:
                exp_selectors = [
                    '.flex.items-center.gap-2 span:contains("Years")',
                    '.flex.items-center.gap-2 span:contains("yrs")',
                    "//div[contains(@class, 'flex items-center gap-2')]//span[contains(text(), 'Years') or contains(text(), 'yrs')]",
                    "//span[contains(text(), 'yrs') or contains(text(), 'years') or contains(text(), 'experience')]"
                ]

                for selector in exp_selectors:
                    try:
                        if selector.startswith('//'):
                            exp_elem = self.driver.find_element(By.XPATH, selector)
                        else:
                            exp_elem = self.driver.find_element(By.CSS_SELECTOR, selector)

                        text = exp_elem.text.strip()
                        if any(keyword in text.lower() for keyword in ['year', 'yrs', 'experience']):
                            job_data.experience = text
                            logger.debug(f"Found experience: {job_data.experience}")
                            break
                    except NoSuchElementException:
                        continue

            if not job_data.location:
                location_selectors = [
                    '.flex.items-center.gap-2 a[href*="jobs-in-"]',
                    'a[href*="jobs-in-"]',
                    "//div[contains(@class, 'flex items-center gap-2')]//a[contains(@href, 'jobs-in-')]",
                    ".location",
                    "[data-testid='location']"
                ]

                for selector in location_selectors:
                    try:
                        if selector.startswith('//'):
                            location_elem = self.driver.find_element(By.XPATH, selector)
                        else:
                            location_elem = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if location_elem.text.strip():
                            job_data.location = location_elem.text.strip()
                            logger.debug(f"Found location: {job_data.location}")
                            break
                    except NoSuchElementException:
                        continue

            logger.info(f"Successfully extracted detailed info for: {job_data.title}")
            return job_data

        except Exception as e:
            logger.error(f"Error extracting job detail data: {e}")
            return job_data

    def go_to_next_page(self) -> bool:
        """Navigate to next page of search results"""
        try:
            # Look for next page button with various selectors
            next_selectors = [
                self.selectors['next_page'],
                'button:contains("Next")',
                '.pagination-next',
                'a[aria-label="Next"]',
                'button[aria-label="Next"]'
            ]

            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_button.is_displayed() and next_button.is_enabled():
                        # Scroll to button
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                        self.random_delay(1, 2)

                        # Click next button
                        try:
                            next_button.click()
                        except ElementClickInterceptedException:
                            self.driver.execute_script("arguments[0].click();", next_button)

                        self.random_delay(3, 5)

                        # Wait for new page to load
                        try:
                            WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
                            )
                            logger.info("Navigated to next page")
                            return True
                        except TimeoutException:
                            logger.warning("Next page did not load properly")
                            return False

                except NoSuchElementException:
                    continue

            logger.info("No next page button found")
            return False

        except Exception as e:
            logger.warning(f"Error navigating to next page: {e}")
            return False

    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, List[Dict]]:
        """Main scraping function with direct URL navigation and click-through extraction"""
        driver = None
        jobs = []
        scraped_count = 0
        page_num = 1

        try:
            logger.info(f"Starting scrape for '{job_title}' in '{location}' - Target: {num_jobs} jobs")

            # Setup driver with retry logic
            try:
                driver = self.setup_driver()
                self.driver = driver
            except Exception as e:
                logger.error(f"Failed to setup driver: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Driver setup failed: {str(e)}"}

            # Navigate directly to search results
            try:
                if not self.navigate_to_search_results(job_title, location):
                    raise Exception("Failed to navigate to search results")
                logger.info("Successfully navigated to search results page")
            except Exception as e:
                logger.error(f"Error navigating to search results: {e}")
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': f"Failed to navigate to search results: {str(e)}"}

            while scraped_count < num_jobs and page_num <= 10:  # Limit to 10 pages max
                logger.info(f"Scraping page {page_num}...")

                # Extract basic job data from search results page
                page_jobs = self.extract_job_cards_data()

                if not page_jobs:
                    # Check if this is a "no results" page vs a loading issue
                    page_title = self.driver.title
                    if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                        logger.info("Search completed successfully - no jobs found for the given criteria")
                        break
                    else:
                        logger.warning("No jobs found on current page - might be a loading issue")
                        break

                logger.info(f"Found {len(page_jobs)} jobs on page {page_num}")

                # Process each job with click-through navigation
                for i, job_data in enumerate(page_jobs):
                    if scraped_count >= num_jobs:
                        break

                    try:
                        if job_data.title and not job_data.title.startswith("Showing"):  # Only process if we got meaningful data
                            logger.info(f"Processing job {i+1}/{len(page_jobs)}: {job_data.title}")

                            # Click through to job detail page and extract detailed information
                            detailed_job_data = self.click_through_to_job_detail(job_data, i)

                            # Validate that we got meaningful data
                            if detailed_job_data.title and not detailed_job_data.title.startswith("Showing"):
                                jobs.append(asdict(detailed_job_data))

                                if '/job/' in detailed_job_data.job_url:
                                    logger.info(f"Scraped detailed job {scraped_count + 1}/{num_jobs}: {detailed_job_data.title}")
                                else:
                                    logger.info(f"Scraped basic job {scraped_count + 1}/{num_jobs}: {detailed_job_data.title}")

                                scraped_count += 1
                            else:
                                logger.warning(f"Skipping job with invalid title: {detailed_job_data.title}")

                            # Add delay between job processing
                            if scraped_count < num_jobs:
                                self.random_delay(1, 2)

                    except Exception as e:
                        logger.error(f"Error processing job {i}: {e}")
                        # Try to return to search results if we're not there
                        try:
                            current_url = self.driver.current_url
                            if '/search/' not in current_url:
                                logger.info("Returning to search results after error")
                                self.driver.back()
                                self.random_delay(2, 3)
                        except:
                            pass
                        continue

                # Try to navigate to next page
                if scraped_count < num_jobs:
                    if not self.go_to_next_page():
                        logger.info("No more pages available")
                        break
                    page_num += 1
                else:
                    break

            logger.info(f"Scraping completed. Total jobs scraped: {len(jobs)}")

            # Provide informative response based on results
            result = {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'requested': num_jobs}

            if len(jobs) == 0:
                # Check if this was a "no results" case
                page_title = self.driver.title if self.driver else ""
                if ': 0 ' in page_title or 'no jobs' in page_title.lower() or '0 job vacancies' in page_title.lower():
                    result['message'] = f"No jobs found for '{job_title}' in '{location}' on Foundit. Try different search terms or location."
                    result['suggestion'] = "Try searching for 'Data Scientist' in 'Bangalore' or 'Mumbai' instead of 'India'"
                else:
                    result['message'] = "No jobs could be extracted. This might be due to page loading issues or changed website structure."

            return result

        except Exception as e:
            logger.error(f"Fatal error during scraping: {e}")
            return {'scraped_jobs': jobs, 'total_scraped': len(jobs), 'error': str(e)}

        finally:
            if driver:
                try:
                    driver.quit()
                    logger.info("Driver closed")
                except Exception as e:
                    logger.warning(f"Error closing driver: {e}")

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Driver closed successfully")
            except Exception as e:
                logger.warning(f"Error closing driver: {e}")

# Global scraper instance
scraper = None

def get_scraper():
    """Get or create scraper instance"""
    global scraper
    if scraper is None:
        scraper = FounditScraper(headless=True)
    return scraper

@app.post("/scrape")
async def scrape_jobs_endpoint(
    job_title: str = Body(..., description="Job title to search for"),
    location: str = Body(..., description="Location to search in"),
    num_jobs: int = Body(5, description="Number of jobs to scrape")
):
    """Scrape jobs from Foundit.in"""
    try:
        logger.info(f"Received scraping request: {job_title} in {location}, {num_jobs} jobs")

        # Validate inputs
        if not job_title or not job_title.strip():
            raise HTTPException(status_code=400, detail="Job title is required")
        if not location or not location.strip():
            raise HTTPException(status_code=400, detail="Location is required")
        if num_jobs <= 0 or num_jobs > 50:
            raise HTTPException(status_code=400, detail="Number of jobs must be between 1 and 50")

        # Get scraper instance
        scraper_instance = FounditScraper(headless=True)

        # Scrape jobs
        result = scraper_instance.scrape_jobs(
            job_title=job_title.strip(),
            location=location.strip(),
            num_jobs=num_jobs
        )

        return JSONResponse(content=result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in scrape endpoint: {e}")
        return JSONResponse(
            status_code=500,
            content={
                'scraped_jobs': [],
                'total_scraped': 0,
                'error': f"Internal server error: {str(e)}"
            }
        )

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Foundit Job Scraper API v3.0", "status": "active"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "foundit-scraper"}

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting Foundit Job Scraper API v3.0...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
