#!/usr/bin/env python3
"""
Performance test script to demonstrate the optimized typing speeds.
This script shows the difference between various typing modes.
"""

import time
from new_glassdoor import ScraperConfig, GlassdoorScraper, HumanBehavior, ScraperLogger

class MockElement:
    """Mock element for testing typing without actual browser"""
    def __init__(self):
        self.text = ""
    
    def clear(self):
        self.text = ""
    
    def send_keys(self, text):
        self.text += text

def test_typing_speed(typing_method, text, description):
    """Test a specific typing method and measure time"""
    print(f"\n🧪 Testing {description}")
    print("-" * 40)
    
    element = MockElement()
    human_behavior = HumanBehavior(ScraperLogger())
    
    start_time = time.time()
    
    if typing_method == 'ultra_fast':
        human_behavior.fast_form_fill(element, text)
    else:
        human_behavior.simulate_typing(element, text, typing_method)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"✅ Completed in {duration:.2f} seconds")
    print(f"📝 Text: '{text}' -> '{element.text}'")
    print(f"⚡ Speed: {len(text)/duration:.1f} chars/second")
    
    return duration

def main():
    print("🚀 Typing Performance Test - Enhanced Glassdoor Scraper")
    print("=" * 60)
    
    # Test data
    job_title = "Senior Software Engineer"
    location = "San Francisco, CA"
    
    print(f"📋 Test Data:")
    print(f"   Job Title: '{job_title}' ({len(job_title)} chars)")
    print(f"   Location: '{location}' ({len(location)} chars)")
    
    # Test different typing speeds
    typing_tests = [
        ('ultra_fast', 'Ultra Fast Form Fill (Recommended)'),
        ('very_fast', 'Very Fast Typing'),
        ('fast', 'Fast Typing'),
        ('normal', 'Normal Typing'),
        ('slow', 'Slow Typing'),
    ]
    
    results = {}
    
    print("\n" + "=" * 60)
    print("🔤 JOB TITLE TYPING TESTS")
    print("=" * 60)
    
    for speed, description in typing_tests:
        duration = test_typing_speed(speed, job_title, description)
        results[f"{speed}_job"] = duration
    
    print("\n" + "=" * 60)
    print("📍 LOCATION TYPING TESTS")
    print("=" * 60)
    
    for speed, description in typing_tests:
        duration = test_typing_speed(speed, location, description)
        results[f"{speed}_location"] = duration
    
    # Calculate total form filling times
    print("\n" + "=" * 60)
    print("📊 TOTAL FORM FILLING TIME COMPARISON")
    print("=" * 60)
    
    for speed, description in typing_tests:
        job_time = results[f"{speed}_job"]
        location_time = results[f"{speed}_location"]
        total_time = job_time + location_time + 1.0  # Add 1 second for navigation delays
        
        print(f"{description:30} | {total_time:6.2f}s | {job_time:5.2f}s + {location_time:5.2f}s + 1.00s")
    
    # Recommendations
    print("\n" + "=" * 60)
    print("💡 RECOMMENDATIONS")
    print("=" * 60)
    
    ultra_fast_total = results['ultra_fast_job'] + results['ultra_fast_location'] + 1.0
    slow_total = results['slow_job'] + results['slow_location'] + 1.0
    improvement = ((slow_total - ultra_fast_total) / slow_total) * 100
    
    print(f"🏆 Best Performance: Ultra Fast Form Fill")
    print(f"⚡ Speed Improvement: {improvement:.1f}% faster than slow typing")
    print(f"⏱️  Total Form Time: {ultra_fast_total:.2f} seconds (well under 30s target)")
    print(f"🛡️  Detection Risk: Minimal (still includes realistic delays)")
    
    print("\n🔧 Configuration Recommendation:")
    print("""
config = ScraperConfig(
    driver_mode='undetected',
    use_ultra_fast_forms=True,    # Enable ultra-fast form filling
    typing_speed='very_fast',     # Fallback speed for other typing
    headless=False,               # Better detection avoidance
    randomize_viewport=True,      # Randomize window size
)
    """)
    
    print("📋 Usage Notes:")
    print("  • Ultra-fast form filling completes in under 5 seconds total")
    print("  • Still includes realistic delays to avoid detection")
    print("  • Maintains human-like behavior patterns")
    print("  • Automatically falls back to configurable typing if needed")

if __name__ == "__main__":
    main()
