#!/usr/bin/env python3
"""
Setup script for enhanced Glassdoor scraper anti-detection capabilities.
This script helps install and configure the necessary dependencies for bypassing Cloudflare.
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package_name, import_name=None):
    """Install a package using pip"""
    if import_name is None:
        import_name = package_name.replace('-', '_')
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} is already installed")
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ Successfully installed {package_name}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package_name}: {e}")
            return False

def main():
    print("🚀 Setting up Enhanced Glassdoor Scraper Anti-Detection")
    print("=" * 60)
    
    # Required packages for anti-detection
    packages = [
        ("undetected-chromedriver", "undetected_chromedriver"),
        ("selenium-stealth", "selenium_stealth"),
        ("seleniumbase", "seleniumbase"),
    ]
    
    success_count = 0
    total_packages = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Installation Summary: {success_count}/{total_packages} packages installed successfully")
    
    if success_count == total_packages:
        print("🎉 All anti-detection packages installed successfully!")
        print("\n📋 Available Driver Modes:")
        print("  • 'undetected' - Uses undetected-chromedriver (Recommended)")
        print("  • 'seleniumbase' - Uses SeleniumBase with UC mode")
        print("  • 'stealth' - Uses selenium-stealth plugin")
        print("  • 'standard' - Enhanced standard driver (fallback)")
        
        print("\n🔧 Configuration Example:")
        print("""
from new_glassdoor import ScraperConfig, GlassdoorScraper

# Configure for maximum stealth
config = ScraperConfig(
    driver_mode='undetected',  # Use undetected ChromeDriver
    headless=False,           # Non-headless for better detection avoidance
    randomize_viewport=True,  # Randomize window size
    use_proxy=False,          # Enable if you have proxies
    proxy_list=[],            # Add your proxy list here
)

scraper = GlassdoorScraper(config)
result = scraper.scrape_jobs("Software Engineer", "San Francisco", 5)
        """)
        
    else:
        print("⚠️  Some packages failed to install. The scraper will fall back to standard mode.")
        print("💡 You can still use the scraper, but anti-detection capabilities will be limited.")
    
    print("\n🛡️  Additional Tips for Avoiding Detection:")
    print("  • Use residential proxies if available")
    print("  • Run scraper during business hours")
    print("  • Add random delays between requests")
    print("  • Don't run too many concurrent sessions")
    print("  • Monitor for Cloudflare challenges and adjust accordingly")
    
    print("\n📚 For more information, check the documentation in new_glassdoor.py")

if __name__ == "__main__":
    main()
