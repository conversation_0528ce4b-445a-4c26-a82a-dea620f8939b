#!/usr/bin/env python3
"""
Example script demonstrating enhanced Glassdoor scraping with anti-detection features.
This script shows how to configure and use the different driver modes for bypassing Cloudflare.
"""

import json
import time
from new_glassdoor import ScraperConfig, GlassdoorScraper

def test_driver_mode(mode, job_title="Data Scientist", location="San Francisco", num_jobs=3):
    """Test a specific driver mode"""
    print(f"\n🧪 Testing {mode.upper()} mode")
    print("-" * 40)
    
    try:
        # Configure scraper for the specific mode with optimized performance
        config = ScraperConfig(
            driver_mode=mode,
            headless=False,  # Non-headless for better detection avoidance
            randomize_viewport=True,
            max_retries=2,
            retry_delay=3,
            # Performance optimizations
            typing_speed='very_fast',
            use_ultra_fast_forms=True,
            # Cloudflare optimization
            cloudflare_timeout=15,  # Reduced from 45s
            cloudflare_max_retries=3,
            progressive_timeout=True,
            fast_challenge_detection=True,
            auto_fallback_drivers=True,
        )
        
        scraper = GlassdoorScraper(config)
        
        start_time = time.time()
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        end_time = time.time()
        
        # Print results
        scraped_jobs = result.get('scraped_jobs', [])
        metadata = result.get('metadata', {})
        
        print(f"✅ Success! Scraped {len(scraped_jobs)} jobs in {end_time - start_time:.2f}s")
        print(f"📊 Success rate: {metadata.get('success_rate', 'N/A')}")
        
        if scraped_jobs:
            print(f"📝 Sample job: {scraped_jobs[0].get('title', 'N/A')} at {scraped_jobs[0].get('company_name', 'N/A')}")
        
        if metadata.get('errors'):
            print(f"⚠️  Errors encountered: {len(metadata['errors'])}")
            
        return True, len(scraped_jobs)
        
    except Exception as e:
        print(f"❌ Failed: {str(e)}")
        return False, 0

def main():
    print("🚀 Enhanced Glassdoor Scraper - Anti-Detection Demo")
    print("=" * 60)
    
    # Test different driver modes
    modes_to_test = ['undetected', 'seleniumbase', 'stealth', 'standard']
    results = {}
    
    for mode in modes_to_test:
        success, job_count = test_driver_mode(mode)
        results[mode] = {'success': success, 'jobs': job_count}
        
        # Add delay between tests to avoid rate limiting
        if mode != modes_to_test[-1]:  # Don't wait after the last test
            print("⏳ Waiting 30 seconds before next test...")
            time.sleep(30)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY RESULTS")
    print("=" * 60)
    
    for mode, result in results.items():
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        jobs = result['jobs']
        print(f"{mode.upper():12} | {status} | Jobs: {jobs}")
    
    # Recommendations
    successful_modes = [mode for mode, result in results.items() if result['success']]
    
    if successful_modes:
        best_mode = max(successful_modes, key=lambda m: results[m]['jobs'])
        print(f"\n🏆 Best performing mode: {best_mode.upper()}")
        print(f"💡 Recommendation: Use driver_mode='{best_mode}' for optimal results")
    else:
        print("\n⚠️  All modes failed. Check your setup and network connection.")
    
    print("\n🛡️  Anti-Detection Tips:")
    print("  • Use non-headless mode when possible")
    print("  • Enable viewport randomization")
    print("  • Add proxies if available")
    print("  • Monitor Cloudflare challenges")
    print("  • Adjust delays based on success rate")

def demo_advanced_configuration():
    """Demonstrate advanced configuration options"""
    print("\n🔧 Advanced Configuration Example")
    print("-" * 40)
    
    # Example with proxy support and optimized performance (add your proxies)
    config = ScraperConfig(
        driver_mode='undetected',
        headless=False,
        randomize_viewport=True,
        use_proxy=False,  # Set to True if you have proxies
        proxy_list=[
            # 'http://proxy1:port',
            # 'http://proxy2:port',
        ],
        max_retries=3,
        retry_delay=5,
        enable_canvas_fingerprinting=True,
        enable_webgl_fingerprinting=True,
        # Performance optimizations
        typing_speed='very_fast',
        use_ultra_fast_forms=True,
    )
    
    print("Configuration created with advanced anti-detection features:")
    print(f"  • Driver mode: {config.driver_mode}")
    print(f"  • Headless: {config.headless}")
    print(f"  • Viewport randomization: {config.randomize_viewport}")
    print(f"  • Canvas fingerprinting protection: {config.enable_canvas_fingerprinting}")
    print(f"  • WebGL fingerprinting protection: {config.enable_webgl_fingerprinting}")
    
    # You can use this config with the scraper
    # scraper = GlassdoorScraper(config)
    # result = scraper.scrape_jobs("Software Engineer", "New York", 5)

if __name__ == "__main__":
    main()
    demo_advanced_configuration()
    
    print("\n📚 For more examples and documentation, see:")
    print("  • new_glassdoor.py - Main scraper implementation")
    print("  • setup_anti_detection.py - Setup script for dependencies")
