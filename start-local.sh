#!/bin/bash

echo "🚀 Starting Job Portal locally with ngrok..."

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Please install ngrok first."
    echo "   Visit: https://ngrok.com/download"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $FRONTEND_PID $NGINX_PID $NGROK_PID 2>/dev/null
    exit
}

trap cleanup INT

# Start backend
echo "🔧 Starting FastAPI backend..."
cd "$(dirname "$0")"
uvicorn main:app --reload --port 8000 &
BACKEND_PID=$!

# Start frontend
echo "🎨 Starting Next.js frontend..."
cd web-portal
npm run dev &
FRONTEND_PID=$!
cd ..

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 5

# Start nginx (if available)
if command -v nginx &> /dev/null; then
    echo "🌐 Starting nginx proxy..."
    nginx -c "$(pwd)/nginx.conf" -p "$(pwd)" &
    NGINX_PID=$!
    PROXY_PORT=8080
else
    echo "⚠️  nginx not found. Using backend directly on port 8000"
    PROXY_PORT=8000
fi

# Wait a bit more for nginx
sleep 3

# Start ngrok tunnel
echo "🌐 Starting ngrok tunnel..."
ngrok http $PROXY_PORT --log=stdout &
NGROK_PID=$!

# Wait for ngrok to start
sleep 3

# Get ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data['tunnels'][0]['public_url'])
except:
    print('Error getting ngrok URL')
")

if [[ $NGROK_URL == *"ngrok"* ]]; then
    echo "🎉 Setup complete!"
    echo ""
    echo "📱 Access your application:"
    echo "   Public URL: $NGROK_URL"
    echo "   Local URL:  http://localhost:$PROXY_PORT"
    echo ""
    echo "🔗 Direct Access:"
    echo "   Frontend:   http://localhost:3000"
    echo "   Backend:    http://localhost:8000"
    echo "   API Docs:   http://localhost:8000/docs"
    echo ""
    echo "📊 Monitoring:"
    echo "   ngrok Web Interface: http://localhost:4040"
    echo ""
    echo "🛑 Press Ctrl+C to stop all services"
else
    echo "❌ Failed to get ngrok URL. Check if ngrok is running properly."
fi

# Keep script running
wait
