#!/usr/bin/env python3
"""
Test script for the enhanced Foundit scraper with debugging capabilities
"""

from new_foundit import FounditScraper
import logging
import sys

def test_basic_scraping():
    """Test basic scraping functionality"""
    print("=== Testing Basic Scraping ===")
    
    try:
        scraper = FounditScraper(headless=True)
        result = scraper.scrape_jobs('Data Scientist', 'Pune', 1)
        
        print(f"Total scraped: {result['total_scraped']}")
        print(f"Jobs found: {len(result['scraped_jobs'])}")
        
        if result['scraped_jobs']:
            job = result['scraped_jobs'][0]
            print(f"Title: {job['title']}")
            print(f"Company: {job['company_name']}")
            print(f"URL: {job['job_url']}")
            print(f"Has job ID in URL: {any(char.isdigit() for char in job['job_url'][-15:])}")
            
            return True
        else:
            print("No jobs found")
            return False
            
    except Exception as e:
        print(f"Error in basic scraping: {e}")
        return False

def test_multiple_jobs():
    """Test scraping multiple jobs"""
    print("\n=== Testing Multiple Jobs ===")
    
    try:
        scraper = FounditScraper(headless=True)
        result = scraper.scrape_jobs('Data Scientist', 'Pune', 3)
        
        print(f"Total scraped: {result['total_scraped']}")
        print(f"Jobs found: {len(result['scraped_jobs'])}")
        
        # Check if we got different jobs
        titles = [job['title'] for job in result['scraped_jobs']]
        companies = [job['company_name'] for job in result['scraped_jobs']]
        urls = [job['job_url'] for job in result['scraped_jobs']]
        
        print(f"Unique titles: {len(set(titles))}")
        print(f"Unique companies: {len(set(companies))}")
        print(f"Unique URLs: {len(set(urls))}")
        
        # Print job details
        for i, job in enumerate(result['scraped_jobs'], 1):
            print(f"\nJob {i}:")
            print(f"  Title: {job['title']}")
            print(f"  Company: {job['company_name']}")
            print(f"  URL: {job['job_url']}")
        
        return len(set(titles)) > 1  # Success if we got different job titles
        
    except Exception as e:
        print(f"Error in multiple jobs test: {e}")
        return False

def main():
    """Run all tests"""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("Starting Foundit scraper tests...")
    
    # Run tests
    tests = [
        ("Basic Scraping", test_basic_scraping),
        ("Multiple Jobs", test_multiple_jobs),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    # Overall result
    all_passed = all(results.values())
    overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
    print(f"\nOverall: {overall_status}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
