from fastapi import FastAP<PERSON>, HTTPException, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from bs4 import BeautifulSoup
import undetected_chromedriver as uc
import time
import random
import logging
from jobspy import scrape_jobs
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Logger setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("NaukriJobDetailScraper")

app = FastAPI()

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://ffb46ce19203.ngrok-free.app",
    "*"  # Allow all origins for development
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class NaukriJobSearchRequest(BaseModel):
    search_term: str
    location: str
    results_wanted: int = 5
    hours_old: Optional[int] = 72
    job_type: Optional[str] = None
    is_remote: Optional[bool] = None
    easy_apply: Optional[bool] = None
    offset: Optional[int] = 0
    verbose: Optional[int] = 2
    proxies: Optional[List[str]] = None

def create_stealth_driver():
    """Create a more stealthy Chrome driver for headless mode"""
    options = uc.ChromeOptions()
    
    # Essential headless options
    options.add_argument('--headless=new')  # Use new headless mode
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-extensions')
    
    # Stealth options to avoid detection
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-web-security')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--disable-ipc-flooding-protection')
    options.add_argument('--disable-renderer-backgrounding')
    options.add_argument('--disable-backgrounding-occluded-windows')
    options.add_argument('--disable-client-side-phishing-detection')
    options.add_argument('--disable-sync')
    options.add_argument('--disable-default-apps')
    options.add_argument('--hide-scrollbars')
    options.add_argument('--mute-audio')
    
    # Window size and user agent
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--start-maximized')
    
    # More realistic user agent
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    options.add_argument(f'--user-agent={random.choice(user_agents)}')
    
    # Disable image loading for faster performance (optional)
    # options.add_argument('--disable-images')
    
    try:
        driver = uc.Chrome(options=options, version_main=None)
        
        # Execute script to remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set additional properties to mimic real browser
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": driver.execute_script("return navigator.userAgent").replace("HeadlessChrome", "Chrome")
        })
        
        return driver
    except Exception as e:
        logger.error(f"Failed to create driver: {e}")
        raise

def wait_for_page_load(driver, timeout=15):
    """Wait for page to fully load with multiple strategies"""
    try:
        # Wait for document ready state
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # Wait for jQuery if present
        try:
            WebDriverWait(driver, 5).until(
                lambda d: d.execute_script("return typeof jQuery === 'undefined' || jQuery.active === 0")
            )
        except:
            pass  # jQuery might not be present
            
        # Small additional wait for dynamic content
        time.sleep(random.uniform(2, 4))
        
    except TimeoutException:
        logger.warning("Page load timeout, continuing anyway")

def scrape_naukri_job_details(url_list: list) -> list:
    driver = None
    details = []
    
    try:
        driver = create_stealth_driver()
        
        for idx, job_url in enumerate(url_list):
            try:
                logger.info(f"Scraping job {idx + 1}/{len(url_list)}: {job_url}")
                
                # Add random delay between requests
                if idx > 0:
                    time.sleep(random.uniform(3, 7))
                
                driver.get(job_url)
                
                # Wait for page to load completely
                wait_for_page_load(driver)
                
                # Try multiple strategies to wait for content
                content_loaded = False
                selectors_to_try = [
                    (By.CLASS_NAME, "styles_jd-header-title__rZwM1"),
                    (By.TAG_NAME, "h1"),
                    (By.CSS_SELECTOR, "[class*='jd-header-title']"),
                    (By.CSS_SELECTOR, "[class*='header-title']")
                ]
                
                for selector_type, selector_value in selectors_to_try:
                    try:
                        WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((selector_type, selector_value))
                        )
                        content_loaded = True
                        logger.info(f"Content loaded using selector: {selector_value}")
                        break
                    except TimeoutException:
                        continue
                
                if not content_loaded:
                    logger.warning(f"Content may not have loaded properly for {job_url}")
                    # Try scrolling to trigger lazy loading
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(2)
                    driver.execute_script("window.scrollTo(0, 0);")
                    time.sleep(2)
                
                # Get page source and parse
                soup = BeautifulSoup(driver.page_source, 'html.parser')
                
                # Debug: Save page source for troubleshooting
                if not soup.find('h1'):
                    logger.warning(f"No h1 found in page source for {job_url}")
                    with open(f"debug_page_{idx}.html", "w", encoding="utf-8") as f:
                        f.write(driver.page_source)
                
                # Extract job ID from URL
                import re
                job_id = None
                m = re.search(r'job-listings-.*-(\d+)', job_url)
                if m:
                    job_id = m.group(1)
                
                # Extract job title with multiple selectors
                title = None
                title_selectors = [
                    'h1.styles_jd-header-title__rZwM1',
                    'h1[class*="jd-header-title"]',
                    'h1[class*="header-title"]',
                    '.styles_jd-header-title__rZwM1',
                    '[class*="jd-header-title"]'
                ]
                
                for selector in title_selectors:
                    h1 = soup.select_one(selector)
                    if h1:
                        title = h1.get_text(strip=True)
                        break
                
                if not title:
                    # Fallback: try any h1 tag
                    h1 = soup.find('h1')
                    if h1:
                        title = h1.get_text(strip=True)
                
                # Extract company name and URL
                company_name = None
                company_url = None
                comp_selectors = [
                    'div.styles_jd-header-comp-name__MvqAI',
                    'div[class*="jd-header-comp-name"]',
                    'div[class*="comp-name"]'
                ]
                
                for selector in comp_selectors:
                    comp_div = soup.select_one(selector)
                    if comp_div:
                        a = comp_div.find('a')
                        if a:
                            company_name = a.get_text(strip=True)
                            company_url = a.get('href')
                        break
                
                # Extract company logo
                company_logo = None
                logo_selectors = [
                    'img.styles_jhc__comp-banner__ynBvr',
                    'img.styles_jh__comp-banner-sticky__svJ2h',
                    'img[class*="comp-banner"]'
                ]
                
                for selector in logo_selectors:
                    img = soup.select_one(selector)
                    if img:
                        company_logo = img.get('src')
                        break
                
                # Extract location
                location = None
                loc_selectors = [
                    'span.styles_jhc__location__W_pVs',
                    'span[class*="location"]'
                ]
                
                for selector in loc_selectors:
                    loc_div = soup.select_one(selector)
                    if loc_div:
                        a = loc_div.find('a')
                        if a:
                            location = a.get_text(strip=True)
                        else:
                            location = loc_div.get_text(strip=True)
                        break
                
                # Extract experience
                experience = None
                exp_selectors = [
                    'div.styles_jhc__exp__k_giM',
                    'div[class*="exp"]'
                ]
                
                for selector in exp_selectors:
                    exp_div = soup.select_one(selector)
                    if exp_div:
                        span = exp_div.find('span')
                        if span:
                            experience = span.get_text(strip=True)
                        else:
                            experience = exp_div.get_text(strip=True)
                        break
                
                # Extract salary
                salary = None
                sal_selectors = [
                    'div.styles_jhc__salary__jdfEC',
                    'div[class*="salary"]'
                ]
                
                for selector in sal_selectors:
                    sal_div = soup.select_one(selector)
                    if sal_div:
                        span = sal_div.find('span')
                        if span:
                            salary = span.get_text(strip=True)
                        else:
                            salary = sal_div.get_text(strip=True)
                        break
                
                # Extract statistics (posted date, openings, applicants)
                posted_date = openings = applicants = None
                stat_selectors = [
                    'span.styles_jhc__stat__PgY67',
                    'span[class*="stat"]'
                ]
                
                for selector in stat_selectors:
                    stats = soup.select(selector)
                    if stats:
                        for stat in stats:
                            label = stat.find('label')
                            value = stat.find('span')
                            if label and value:
                                ltxt = label.get_text(strip=True).lower()
                                vtxt = value.get_text(strip=True)
                                if 'posted' in ltxt:
                                    posted_date = vtxt
                                elif 'openings' in ltxt:
                                    openings = vtxt
                                elif 'applicants' in ltxt:
                                    applicants = vtxt
                        break
                
                # Extract job description
                job_description = None
                desc_selectors = [
                    'div.styles_JDC__dang-inner-html__h0K4t',
                    'div[class*="dang-inner-html"]',
                    'div[class*="job-description"]'
                ]
                
                for selector in desc_selectors:
                    desc_div = soup.select_one(selector)
                    if desc_div:
                        job_description = desc_div.get_text(separator='\n', strip=True)
                        break
                
                # Extract additional details (role, industry, etc.)
                role = industry_type = department = employment_type = role_category = None
                detail_selectors = [
                    'div.styles_details__Y424J',
                    'div[class*="details"]'
                ]
                
                for selector in detail_selectors:
                    details_divs = soup.select(selector)
                    if details_divs:
                        for d in details_divs:
                            label = d.find('label')
                            span = d.find('span')
                            if label and span:
                                ltxt = label.get_text(strip=True).lower()
                                vtxt = span.get_text(strip=True)
                                if 'role:' in ltxt:
                                    role = vtxt
                                elif 'industry type:' in ltxt:
                                    industry_type = vtxt
                                elif 'department:' in ltxt:
                                    department = vtxt
                                elif 'employment type:' in ltxt:
                                    employment_type = vtxt
                                elif 'role category:' in ltxt:
                                    role_category = vtxt
                        break
                
                # Extract education requirements
                education_ug = education_pg = None
                edu_selectors = [
                    'div.styles_education__KXFkO',
                    'div[class*="education"]'
                ]
                
                for selector in edu_selectors:
                    edu_div = soup.select_one(selector)
                    if edu_div:
                        ugs = edu_div.find_all('div', class_='styles_details__Y424J')
                        for ug in ugs:
                            label = ug.find('label')
                            span = ug.find('span')
                            if label and span:
                                ltxt = label.get_text(strip=True).lower()
                                vtxt = span.get_text(strip=True)
                                if 'ug:' in ltxt:
                                    education_ug = vtxt
                                elif 'pg:' in ltxt:
                                    education_pg = vtxt
                        break
                
                # Extract key skills
                key_skills = []
                skill_selectors = [
                    'div.styles_key-skill__GIPn_',
                    'div[class*="key-skill"]'
                ]
                
                for selector in skill_selectors:
                    skills_div = soup.select_one(selector)
                    if skills_div:
                        skill_links = skills_div.find_all('a', class_='styles_chip__7YCfG')
                        if not skill_links:
                            skill_links = skills_div.find_all('a')
                        
                        for a in skill_links:
                            span = a.find('span')
                            if span:
                                key_skills.append(span.get_text(strip=True))
                            else:
                                key_skills.append(a.get_text(strip=True))
                        break
                
                if not key_skills:
                    key_skills = None
                
                # Extract about company
                about_company = None
                about_selectors = [
                    'section.styles_about-company__lOsvW',
                    'section[class*="about-company"]'
                ]
                
                for selector in about_selectors:
                    about_sec = soup.select_one(selector)
                    if about_sec:
                        about_div = about_sec.find('div', class_='styles_detail__U2rw4')
                        if not about_div:
                            about_div = about_sec.find('div')
                        if about_div:
                            about_company = about_div.get_text(strip=True)
                        break
                
                # Extract company website and address
                company_website = company_address = None
                comp_info_selectors = [
                    'div.styles_comp-info-detail__sO7Aw',
                    'div[class*="comp-info-detail"]'
                ]
                
                for selector in comp_info_selectors:
                    comp_info = soup.select(selector)
                    if comp_info:
                        for ci in comp_info:
                            label = ci.find('label')
                            span = ci.find('span')
                            if label and span:
                                ltxt = label.get_text(strip=True).lower()
                                vtxt = span.get_text(strip=True)
                                if 'link:' in ltxt:
                                    a = span.find('a')
                                    if a:
                                        company_website = a.get('href')
                                elif 'address:' in ltxt:
                                    company_address = vtxt
                        break
                
                # Extract company rating and reviews
                company_rating = None
                company_reviews_count = None
                company_reviews_url = None
                company_overview_url = None
                
                if comp_div:
                    rating_div = comp_div.find('div', class_='styles_rating-wrapper__jPmOo')
                    if not rating_div:
                        rating_div = comp_div.find('div', class_=lambda x: x and 'rating' in x)
                    
                    if rating_div:
                        a = rating_div.find('a')
                        if a:
                            company_reviews_url = a.get('href')
                            rating_span = a.find('span', class_='styles_amb-rating__4UyFL')
                            if not rating_span:
                                rating_span = a.find('span', class_=lambda x: x and 'rating' in x)
                            if rating_span:
                                company_rating = rating_span.get_text(strip=True)
                            
                            reviews_span = a.find('span', class_='styles_amb-reviews__0J1e3')
                            if not reviews_span:
                                reviews_span = a.find('span', class_=lambda x: x and 'reviews' in x)
                            if reviews_span:
                                company_reviews_count = reviews_span.get_text(strip=True)
                
                # Company overview URL
                logo_link = soup.find('a', href=True, target="_blank")
                if logo_link and logo_link.get('href') and 'overview' in logo_link['href']:
                    company_overview_url = logo_link['href']
                
                # Create job detail object
                job_detail = {
                    "job_id": job_id,
                    "title": title,
                    "company_name": company_name,
                    "company_url": company_url,
                    "company_logo": company_logo,
                    "company_rating": company_rating,
                    "company_reviews_count": company_reviews_count,
                    "company_reviews_url": company_reviews_url,
                    "company_overview_url": company_overview_url,
                    "location": location,
                    "experience": experience,
                    "salary": salary,
                    "posted_date": posted_date,
                    "openings": openings,
                    "applicants": applicants,
                    "job_description": job_description,
                    "role": role,
                    "industry_type": industry_type,
                    "department": department,
                    "employment_type": employment_type,
                    "role_category": role_category,
                    "education_ug": education_ug,
                    "education_pg": education_pg,
                    "key_skills": key_skills,
                    "about_company": about_company,
                    "company_website": company_website,
                    "company_address": company_address,
                    "jd_url": job_url
                }
                
                details.append(job_detail)
                logger.info(f"Successfully scraped job: {title or 'Unknown Title'}")
                
            except Exception as e:
                logger.error(f"Failed to extract job from {job_url}: {e}")
                # Save debug info
                try:
                    with open(f"error_debug_{idx}.html", "w", encoding="utf-8") as f:
                        f.write(driver.page_source if driver else "No driver available")
                except:
                    pass
                continue
                
    except Exception as e:
        logger.error(f"Critical error in scraping function: {e}")
        raise
    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass
    
    return details

@app.post("/scrape-naukri/")
async def scrape_naukri(request: NaukriJobSearchRequest = Body(...)):
    params = request.dict(exclude_none=True)
    params["site_name"] = ["naukri"]
    
    try:
        jobs = scrape_jobs(**params)
        if jobs.empty or 'job_url' not in jobs.columns:
            raise HTTPException(status_code=404, detail="No Naukri jobs found.")
        
        url_list = jobs['job_url'].dropna().unique().tolist()
        logger.info(f"Found {len(url_list)} unique job URLs to scrape")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching job URLs: {e}")
    
    details = scrape_naukri_job_details(url_list)
    
    if not details:
        raise HTTPException(status_code=404, detail="No job details could be scraped.")
    
    return {"message": f"Scraped {len(details)} Naukri job details", "job_details": details}

@app.options("/scrape-naukri/")
async def options_naukri():
    return {"message": "OK"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("naukri_job_detail_scraper:app", host="0.0.0.0", port=8003, reload=True)