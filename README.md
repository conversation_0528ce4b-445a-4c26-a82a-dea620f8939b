# Job Scraper Project: Setup & Usage Guide

## How to Run the Project

### 1. Backend (FastAPI Scraper)

- **Install dependencies:**
  ```sh
  pip install -r requirements.txt
  ```
- **Run the backend server:**
  ```sh
  uvicorn main:app --reload
  ```
  This starts the backend at `http://localhost:8000`.
- **(Optional) Expose with ngrok:**
  ```sh
  ngrok http 8000
  ```
  This gives you a public URL (e.g., `https://xxxx-xx-xx-xx.ngrok-free.app`).

### 2. Frontend (Next.js)

- **Install dependencies:**
  ```sh
  cd web-portal
  npm install
  ```
- **Run the frontend:**
  ```sh
  npm run dev
  ```
  This starts the frontend at `http://localhost:3000`.

---

## Updating the Backend URL in the Frontend

If your backend ngrok URL changes, update the API URL in the frontend code:

1. **Find the API URL:**
   - Look for environment variables (e.g., `.env.local`) or direct API URLs in `web-portal/src/` or config files.
2. **Update the URL:**
   - Replace the old ngrok URL with the new one everywhere it appears.
   - Example:
     ```js
     // Old
     const API_URL = "https://old-ngrok-url.ngrok-free.app";
     // New
     const API_URL = "https://new-ngrok-url.ngrok-free.app";
     ```

---

## API Endpoints Summary

| Scraper      | Endpoint                          | Method | Example Body (JSON)                                          |
| ------------ | --------------------------------- | ------ | ------------------------------------------------------------ |
| Foundit      | /foundit/scrape_foundit           | POST   | job_title, location, num_jobs                                |
| Glassdoor    | /glassdoor/scrape_jobs_parallel   | POST   | job_title, location, num_jobs                                |
| SimplyHired  | /simplyhired/scrape_simplyhired   | POST   | job_title, location, num_jobs, headless, detailed_extraction |
| ZipRecruiter | /ziprecruiter/scrape_ziprecruiter | POST   | job_title, location, num_jobs, headless                      |
| LinkedIn     | /scrape-linkedin/                 | POST   | site_name, search_term, location, ...                        |
| Indeed       | /scrape-indeed/                   | POST   | site_name, search_term, location, ...                        |

**Replace** `https://YOUR_NGROK_URL` with your current ngrok URL in all examples below.

---

## Example Usage for Each Scraper

### Foundit Scraper

- **Endpoint:** `POST /foundit/scrape_foundit`
- **Example JSON:**
  ```json
  { "job_title": "Data Scientist", "location": "India", "num_jobs": 5 }
  ```
- **cURL:**
  ```sh
  curl -X POST https://YOUR_NGROK_URL/foundit/scrape_foundit -H "Content-Type: application/json" -d "{\"job_title\": \"Data Scientist\", \"location\": \"India\", \"num_jobs\": 5}"
  ```
- **Thunder Client:**
  - Method: POST
  - URL: https://YOUR_NGROK_URL/foundit/scrape_foundit
  - Body: JSON (see above)

### Glassdoor Scraper

- **Endpoint:** `POST /glassdoor/scrape_jobs_parallel`
- **Example JSON:**
  ```json
  { "job_title": "Data Scientist", "location": "India", "num_jobs": 5 }
  ```
- **cURL:**
  ```sh
  curl -X POST https://YOUR_NGROK_URL/glassdoor/scrape_jobs_parallel -H "Content-Type: application/json" -d "{\"job_title\": \"Data Scientist\", \"location\": \"India\", \"num_jobs\": 5}"
  ```
- **Thunder Client:**
  - Method: POST
  - URL: https://YOUR_NGROK_URL/glassdoor/scrape_jobs_parallel
  - Body: JSON (see above)

### SimplyHired Scraper

- **Endpoint:** `POST /simplyhired/scrape_simplyhired`
- **Example JSON:**
  ```json
  {
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5,
    "headless": true,
    "detailed_extraction": true
  }
  ```
- **cURL:**
  ```sh
  curl -X POST https://YOUR_NGROK_URL/simplyhired/scrape_simplyhired -H "Content-Type: application/json" -d "{\"job_title\": \"Data Scientist\", \"location\": \"India\", \"num_jobs\": 5, \"headless\": true, \"detailed_extraction\": true}"
  ```
- **Thunder Client:**
  - Method: POST
  - URL: https://YOUR_NGROK_URL/simplyhired/scrape_simplyhired
  - Body: JSON (see above)

### ZipRecruiter Scraper

- **Endpoint:** `POST /ziprecruiter/scrape_ziprecruiter`
- **Example JSON:**
  ```json
  {
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5,
    "headless": true
  }
  ```
- **cURL:**
  ```sh
  curl -X POST https://YOUR_NGROK_URL/ziprecruiter/scrape_ziprecruiter -H "Content-Type: application/json" -d "{\"job_title\": \"Data Scientist\", \"location\": \"India\", \"num_jobs\": 5, \"headless\": true}"
  ```
- **Thunder Client:**
  - Method: POST
  - URL: https://YOUR_NGROK_URL/ziprecruiter/scrape_ziprecruiter
  - Body: JSON (see above)

### LinkedIn Scraper (via main.py)

- **Endpoint:** `POST /scrape-linkedin/`
- **Example JSON:**
  ```json
  {
    "site_name": "linkedin",
    "search_term": "Data Scientist",
    "location": "India",
    "results_wanted": 5,
    "hours_old": 72,
    "job_type": null,
    "is_remote": null,
    "easy_apply": null,
    "offset": 0,
    "verbose": 2,
    "linkedin_fetch_description": true,
    "proxies": null
  }
  ```
- **cURL:**
  ```sh
  curl -X POST https://YOUR_NGROK_URL/scrape-linkedin/ -H "Content-Type: application/json" -d "{\"site_name\": \"linkedin\", \"search_term\": \"Data Scientist\", \"location\": \"India\", \"results_wanted\": 5, \"hours_old\": 72, \"job_type\": null, \"is_remote\": null, \"easy_apply\": null, \"offset\": 0, \"verbose\": 2, \"linkedin_fetch_description\": true, \"proxies\": null}"
  ```
- **Thunder Client:**
  - Method: POST
  - URL: https://YOUR_NGROK_URL/scrape-linkedin/
  - Body: JSON (see above)

### Indeed Scraper (via main.py)

- **Endpoint:** `POST /scrape-indeed/`
- **Example JSON:**
  ```json
  {
    "site_name": "indeed",
    "search_term": "Data Scientist",
    "location": "India",
    "results_wanted": 5,
    "country_indeed": "india",
    "hours_old": 72,
    "job_type": null,
    "is_remote": null,
    "easy_apply": null,
    "offset": 0,
    "verbose": 2,
    "linkedin_fetch_description": null,
    "proxies": null
  }
  ```
- **cURL:**
  ```sh
  curl -X POST https://YOUR_NGROK_URL/scrape-indeed/ -H "Content-Type: application/json" -d "{\"site_name\": \"indeed\", \"search_term\": \"Data Scientist\", \"location\": \"India\", \"results_wanted\": 5, \"country_indeed\": \"india\", \"hours_old\": 72, \"job_type\": null, \"is_remote\": null, \"easy_apply\": null, \"offset\": 0, \"verbose\": 2, \"linkedin_fetch_description\": null, \"proxies\": null}"
  ```
- **Thunder Client:**
  - Method: POST
  - URL: https://YOUR_NGROK_URL/scrape-indeed/
  - Body: JSON (see above)

---

# scraper-backend

# Job Scraper API Usage Guide

This guide shows how to use the POST scrape endpoints for each job scraper via `curl` and Thunder Client, assuming your backend is running at:

```
https://7c12-103-247-7-136.ngrok-free.app/
```

---

## 1. Foundit Scraper

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/foundit/scrape_foundit
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/foundit/scrape_foundit \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/foundit/scrape_foundit
- Body: JSON (see above)

---

## 2. Glassdoor Scraper (Parallel)

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/glassdoor/scrape_jobs_parallel
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/glassdoor/scrape_jobs_parallel \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/glassdoor/scrape_jobs_parallel
- Body: JSON (see above)

---

## 3. SimplyHired Scraper

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/simplyhired/scrape_simplyhired
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5,
  "headless": true,
  "detailed_extraction": true
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/simplyhired/scrape_simplyhired \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5,
    "headless": true,
    "detailed_extraction": true
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/simplyhired/scrape_simplyhired
- Body: JSON (see above)

---

## 4. ZipRecruiter Scraper

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/ziprecruiter/scrape_ziprecruiter
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5,
  "headless": true
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/ziprecruiter/scrape_ziprecruiter \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5,
    "headless": true
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/ziprecruiter/scrape_ziprecruiter
- Body: JSON (see above)

---

## 5. LinkedIn Scraper (Detailed, via main.py)

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/scrape-linkedin/
```

**Example JSON Body:**

```json
{
  "site_name": "linkedin",
  "search_term": "Data Scientist",
  "location": "India",
  "results_wanted": 5,
  "hours_old": 72,
  "job_type": null,
  "is_remote": null,
  "easy_apply": null,
  "offset": 0,
  "verbose": 2,
  "linkedin_fetch_description": true,
  "proxies": null
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/scrape-linkedin/ \
  -H "Content-Type: application/json" \
  -d '{
    "site_name": "linkedin",
    "search_term": "Data Scientist",
    "location": "India",
    "results_wanted": 5,
    "hours_old": 72,
    "job_type": null,
    "is_remote": null,
    "easy_apply": null,
    "offset": 0,
    "verbose": 2,
    "linkedin_fetch_description": true,
    "proxies": null
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/scrape-linkedin/
- Body: JSON (see above)

---

## 6. Indeed Scraper (via main.py)

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/scrape-indeed/
```

**Example JSON Body:**

```json
{
  "site_name": "indeed",
  "search_term": "Data Scientist",
  "location": "India",
  "results_wanted": 5,
  "country_indeed": "india",
  "hours_old": 72,
  "job_type": null,
  "is_remote": null,
  "easy_apply": null,
  "offset": 0,
  "verbose": 2,
  "linkedin_fetch_description": null,
  "proxies": null
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/scrape-indeed/ \
  -H "Content-Type: application/json" \
  -d '{
    "site_name": "indeed",
    "search_term": "Data Scientist",
    "location": "India",
    "results_wanted": 5,
    "country_indeed": "india",
    "hours_old": 72,
    "job_type": null,
    "is_remote": null,
    "easy_apply": null,
    "offset": 0,
    "verbose": 2,
    "linkedin_fetch_description": null,
    "proxies": null
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/scrape-indeed/
- Body: JSON (see above)

---

## Notes

- All endpoints expect `Content-Type: application/json`.
- Adjust `job_title`, `location`, and other parameters as needed.
- For Thunder Client, paste the JSON body in the "Body" tab and select "JSON".
- For more details on each scraper's response, see the respective Python file.
# Job_Portal_Aggregator
