import asyncio
import json
import logging
import os
import re
import time
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor, as_completed
from contextlib import contextmanager
from dataclasses import dataclass, field, asdict
from datetime import datetime
from itertools import cycle
from typing import Dict, List, Optional, Union, Any
from urllib.parse import urljoin, urlparse

import uvicorn
from bs4 import BeautifulSoup, NavigableString
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel, field_validator
from selenium import webdriver
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, WebDriverException,
    ElementClickInterceptedException, StaleElementReferenceException
)
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from fastapi.middleware.cors import CORSMiddleware
from selenium.webdriver.common.action_chains import ActionChains
import random
from math import ceil
import signal

# Configuration Management
@dataclass
class ScraperConfig:
    """Configuration class for the scraper"""
    chrome_driver_path: str = '/Users/<USER>/Desktop/job_portal/chromedriver'
    max_workers: int = 3
    default_timeout: int = 20
    page_load_timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 2
    
    # Selectors with fallbacks
    selectors: Dict[str, List[str]] = field(default_factory=lambda: {
        'title': [
            "h1[id^='jd-job-title-']",
            "h1[data-test='job-title']",
            ".JobDetails_jobTitle__Nw_N2",
            "h1.css-1qaijid",
            "h1"
        ],
        'company': [
            "h4.EmployerProfile_employerNameHeading__bXBYr",
            "[data-test='employer-name']",
            ".EmployerProfile_profileContainer__d6vLt h4",
            ".EmployerProfile_employerNameHeading__bXBYr h4",
            "h4"
        ],
        'location': [
            "div[data-test='location']",
            ".JobDetails_location__mSg5h",
            "[data-test='job-location']"
        ],
        'salary': [
            "div[data-test='detailSalary']",
            ".JobDetails_salary__6VyJK",
            "[data-test='salary']"
        ],
        'easy_apply': [
            "button[data-test='easyApply']",
            "button[data-test='apply-button']",
            "button.css-1n6j6mr"
        ],
        'company_logo': [
            ".EmployerProfile_profileContainer__63w3R img",
            ".EmployerProfile_logo__3xqON img",
            "img[alt*='logo']"
        ],
        'job_description': [
            "div.JobDetails_jobDescription__uW_fK",
            ".JobDetails_jobDescription__6VeBn",
            "[data-test='jobDescriptionContent']"
        ],
        'show_more': [
            "button[data-test='show-more-cta']",
            "button[data-test='show-more']",
            "button.css-1gpqj0y"
        ],
        'load_more': [
            "button[data-test='load-more']",
            "button[data-test='pagination-footer-next']",
            "button.css-1gpqj0y"
        ],
        'job_links': [
            "a.JobCard_jobTitle__GLyJ1[data-test='job-title']",
            "a[data-test='job-title']",
            ".JobCard_jobTitle__rw2J1 a"
        ]
    })
    
    user_agents: List[str] = field(default_factory=lambda: [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ])

# Pydantic Models for API
class JobPosting(BaseModel):
    title: str
    company_name: Optional[str] = None
    location: Optional[str] = None
    salary: Optional[str] = None
    job_type: Optional[str] = None
    pay: Optional[str] = None
    work_location: Optional[str] = None
    benefits: Optional[Union[str, List[str]]] = None
    schedule: Optional[Union[str, List[str]]] = None
    education: Optional[str] = None
    most_relevant_skills: Optional[List[str]] = field(default_factory=list)
    other_relevant_skills: Optional[List[str]] = field(default_factory=list)
    easy_apply: bool = False
    company_logo: Optional[str] = None
    job_description: Optional[str] = None
    extra_sections: Dict[str, Any] = field(default_factory=dict)
    job_id: Optional[str] = None
    jd_url: Optional[str] = None

    @field_validator('title')
    @classmethod
    def title_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Job title cannot be empty')
        return v.strip()

    @field_validator('salary', 'pay')
    @classmethod
    def normalize_currency(cls, v):
        if v:
            return re.sub(r'[^\d,.-₹$€£\s]', '', v).strip()
        return v

    @field_validator('most_relevant_skills', 'other_relevant_skills', mode='before')
    @classmethod
    def ensure_list(cls, v):
        if isinstance(v, str):
            return [skill.strip() for skill in v.split(',') if skill.strip()]
        return v or []

class ScrapeRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = Query(5, ge=1, le=50)

class ScrapeResponse(BaseModel):
    scraped_jobs: List[JobPosting]
    metadata: Dict[str, Any]

# Enhanced Logging
class ScraperLogger:
    def __init__(self, name: str = "GlassdoorScraper"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def info(self, msg: str, **kwargs):
        self.logger.info(msg, extra=kwargs)
    
    def error(self, msg: str, **kwargs):
        self.logger.error(msg, extra=kwargs)
    
    def warning(self, msg: str, **kwargs):
        self.logger.warning(msg, extra=kwargs)
    
    def debug(self, msg: str, **kwargs):
        self.logger.debug(msg, extra=kwargs)

# Smart Retry Handler
class SmartRetryHandler:
    def __init__(self, logger: ScraperLogger):
        self.logger = logger
        self.retry_strategies = {
            'timeout': self._handle_timeout,
            'stale_element': self._handle_stale_element,
            'no_such_element': self._handle_no_such_element,
            'click_intercepted': self._handle_click_intercepted,
            'general': self._handle_general_error
        }
    
    def determine_retry_strategy(self, exception: Exception) -> str:
        if isinstance(exception, TimeoutException):
            return 'timeout'
        elif isinstance(exception, StaleElementReferenceException):
            return 'stale_element'
        elif isinstance(exception, NoSuchElementException):
            return 'no_such_element'
        elif isinstance(exception, ElementClickInterceptedException):
            return 'click_intercepted'
        return 'general'
    
    def _handle_timeout(self, delay: int) -> int:
        self.logger.warning(f"Timeout occurred, retrying with {delay * 2}s delay")
        return delay * 2
    
    def _handle_stale_element(self, delay: int) -> int:
        self.logger.warning("Stale element reference, refreshing page state")
        return delay
    
    def _handle_no_such_element(self, delay: int) -> int:
        self.logger.warning("Element not found, trying fallback selectors")
        return delay
    
    def _handle_click_intercepted(self, delay: int) -> int:
        self.logger.warning("Click intercepted, scrolling to element")
        return delay
    
    def _handle_general_error(self, delay: int) -> int:
        return delay * 2

def safe_execute_with_retry(func, retries: int = 3, delay: int = 2, logger: ScraperLogger = None):
    """Execute function with smart retry logic"""
    retry_handler = SmartRetryHandler(logger or ScraperLogger())
    
    for attempt in range(retries):
        try:
            return func()
        except Exception as e:
            if attempt == retries - 1:
                logger.error(f"Failed after {retries} attempts: {str(e)}")
                return None
            
            strategy = retry_handler.determine_retry_strategy(e)
            delay = retry_handler.retry_strategies[strategy](delay)
            logger.warning(f"Attempt {attempt + 1} failed: {str(e)}, retrying in {delay}s")
            time.sleep(delay)
    
    return None

# Improved Driver Manager
class DriverManager:
    def __init__(self, config: ScraperConfig, logger: ScraperLogger):
        self.config = config
        self.logger = logger
        self.user_agent_cycle = cycle(config.user_agents)
    
    def create_driver(self) -> webdriver.Chrome:
        """Optimized Chrome driver for speed and dynamic content scraping"""
        options = Options()
        
        # Core performance options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-web-security')
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--hide-scrollbars')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--log-level=3')
        options.add_argument('--silent')
        # Anti-detection
        options.add_argument(f'--user-agent={next(self.user_agent_cycle)}')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        # Block images and media for speed
        prefs = {
            "profile.default_content_setting_values": {
                "images": 2,  # Block images
                "media_stream": 2,  # Block media
                "stylesheets": 1,  # Allow CSS (needed for selectors)
            },
            "profile.managed_default_content_settings": {
                "images": 2
            }
        }
        options.add_experimental_option("prefs", prefs)
        
        # Create driver
        service = Service(self.config.chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        
        # Anti-detection JS
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
            window.chrome = {runtime: {}};
        """)
        
        # Set shorter timeouts
        driver.set_page_load_timeout(15)  # Reduced from 30
        driver.implicitly_wait(5)         # Reduced from 10
        
        self.logger.info("Optimized Chrome driver created successfully")
        return driver

# Enhanced Field Extractor
class FieldExtractor:
    def __init__(self, config: ScraperConfig, logger: ScraperLogger):
        self.config = config
        self.logger = logger
        self.section_map = {
            'responsibilities': 'responsibilities',
            'key responsibilities': 'responsibilities',
            'qualifications': 'qualifications',
            "what we're looking for": 'requirements',
            'what we are looking for': 'requirements',
            'requirements': 'requirements',
            "what you'll gain": 'perks',
            'what you will gain': 'perks',
            'benefits': 'benefits',
            'schedule': 'schedule',
            'job type': 'jobType',
            'type': 'jobType',
            'contract length': 'contractLength',
            'pay': 'pay',
            'stipend': 'pay',
            'work location': 'workLocation',
            'location': 'workLocation',
            'expected start date': 'expectedStartDate',
            'education': 'education',
            'most relevant skills': 'mostRelevantSkills',
            'other relevant skills': 'otherRelevantSkills',
            'time type': 'timeType',
            'job family group': 'jobFamilyGroup',
            'job family': 'jobFamily',
            'what experience is mandatory': 'mandatoryExperience',
            'what experience is beneficial (but optional)': 'beneficialExperience',
            'what we offer': 'perks',
            'application questions': 'applicationQuestions',
            'application deadline': 'applicationDeadline',
        }
    
    def safe_extract_text(self, driver: webdriver.Chrome, selectors: List[str], attribute: str = None) -> Optional[str]:
        """Extract text using fallback selectors"""
        for selector in selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                if attribute:
                    result = element.get_attribute(attribute)
                else:
                    result = element.text.strip()
                
                if result:
                    return result
                    
            except NoSuchElementException:
                continue
            except Exception as e:
                self.logger.debug(f"Error with selector {selector}: {str(e)}")
                continue
        
        return None
    
    def extract_job_description_sections(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Enhanced job description parsing"""
        sections = {}
        desc_div = soup.find("div", class_="JobDetails_jobDescription__uW_fK")
        
        if not desc_div:
            # Try fallback selectors
            for selector in self.config.selectors['job_description']:
                desc_div = soup.select_one(selector.replace('div.', '').replace('div', ''))
                if desc_div:
                    break
        
        if not desc_div:
            return sections
        
        # Process HTML structure
        current_section = None
        current_content = []
        
        for element in desc_div.descendants:
            if hasattr(element, 'name'):
                if element.name in ['h1', 'h2', 'h3', 'h4', 'b', 'strong']:
                    # Save previous section
                    if current_section and current_content:
                        sections[current_section] = self._process_section_content(current_content)
                    
                    # Start new section
                    section_text = element.get_text(strip=True).lower().rstrip(':')
                    current_section = self.section_map.get(section_text, section_text)
                    current_content = []
                    
                elif element.name in ['p', 'li', 'div'] and current_section:
                    text = element.get_text(strip=True)
                    if text:
                        current_content.append(text)
        
        # Save last section
        if current_section and current_content:
            sections[current_section] = self._process_section_content(current_content)
        
        # Enhanced extraction for specific fields
        self._extract_work_location_from_bold(sections, desc_div)
        self._extract_education_from_text(sections, desc_div)
        self._extract_skills_from_sections(sections, desc_div)
        
        return sections
    
    def _process_section_content(self, content: List[str]) -> Union[str, List[str]]:
        """Process section content based on type"""
        if len(content) == 1:
            return content[0]
        elif len(content) > 1:
            # Check if it looks like a list
            if all(len(item) < 200 for item in content):
                return content
            else:
                return ' '.join(content)
        return ""
    
    def _extract_work_location_from_bold(self, sections: Dict[str, Any], desc_div):
        """Extract work location from bold tags like <b>Locations:</b>"""
        try:
            for b_tag in desc_div.find_all('b'):
                text = b_tag.get_text(strip=True).lower()
                if 'location' in text:
                    # Get the text after the bold tag
                    next_sibling = b_tag.next_sibling
                    if next_sibling and isinstance(next_sibling, NavigableString):
                        location_text = next_sibling.strip()
                        if location_text:
                            sections['work_location'] = location_text
                            break
                    
                    # Alternative: get parent text and extract location
                    parent_text = b_tag.parent.get_text(strip=True)
                    if '|' in parent_text:
                        # Extract location part after "Locations:"
                        location_match = re.search(r'locations?:\s*([^|]+(?:\|[^|]+)*)', parent_text, re.IGNORECASE)
                        if location_match:
                            sections['work_location'] = location_match.group(1).strip()
                            break
        except Exception as e:
            self.logger.debug(f"Error extracting work location from bold: {e}")
    
    def _extract_education_from_text(self, sections: Dict[str, Any], desc_div):
        """Extract education requirements from job description text"""
        try:
            desc_text = desc_div.get_text(separator=' ')
            
            # Look for education patterns
            education_patterns = [
                r"(master['']?s degree|phd|bachelor['']?s degree|education requirements?:?)([^.]+)",
                r"(degree in|studies in|background in)([^.]+)",
                r"(minimum|required|preferred).*?(degree|education|qualification)([^.]+)",
                r"(master['']?s|phd|bachelor['']?s).*?(computer science|data science|mathematics|statistics|engineering)([^.]+)"
            ]
            
            for pattern in education_patterns:
                match = re.search(pattern, desc_text, re.IGNORECASE)
                if match:
                    education_text = match.group(0).strip()
                    if len(education_text) > 10:  # Ensure it's substantial
                        sections['education'] = education_text
                        break
        except Exception as e:
            self.logger.debug(f"Error extracting education: {e}")
    
    def _extract_skills_from_sections(self, sections: Dict[str, Any], desc_div):
        """Extract skills from various sections in job description"""
        try:
            # Look for skills in "Key Competencies", "What You'll Bring", etc.
            skills_sections = ['key competencies', 'what you\'ll bring', 'requirements', 'qualifications', 'skills']
            
            for section_name in skills_sections:
                # Find section by text content
                for element in desc_div.find_all(['h2', 'h3', 'b', 'strong']):
                    if section_name in element.get_text(strip=True).lower():
                        # Extract skills from next sibling or parent
                        skills = self._extract_skills_from_element(element)
                        if skills:
                            if 'most relevant skills' not in sections:
                                sections['most_relevant_skills'] = skills
                            elif 'other relevant skills' not in sections:
                                sections['other_relevant_skills'] = skills
                            break
            
            # Also look for bullet points with skills
            if 'most_relevant_skills' not in sections:
                skills_from_bullets = self._extract_skills_from_bullets(desc_div)
                if skills_from_bullets:
                    sections['most_relevant_skills'] = skills_from_bullets
                    
        except Exception as e:
            self.logger.debug(f"Error extracting skills: {e}")
    
    def _extract_skills_from_element(self, element) -> List[str]:
        """Extract skills from a specific element and its siblings"""
        skills = []
        try:
            # Look for list items after the element
            next_sibling = element.next_sibling
            while next_sibling:
                if hasattr(next_sibling, 'name'):
                    if next_sibling.name in ['ul', 'ol']:
                        for li in next_sibling.find_all('li'):
                            skill_text = li.get_text(strip=True)
                            if skill_text and len(skill_text) > 3:
                                skills.append(skill_text)
                        break
                    elif next_sibling.name in ['p', 'div']:
                        text = next_sibling.get_text(strip=True)
                        if text and len(text) < 200:  # Likely a skill if short
                            skills.append(text)
                next_sibling = next_sibling.next_sibling
        except Exception as e:
            self.logger.debug(f"Error extracting skills from element: {e}")
        return skills
    
    def _extract_skills_from_bullets(self, desc_div) -> List[str]:
        """Extract skills from bullet points in the description"""
        skills = []
        try:
            for ul in desc_div.find_all(['ul', 'ol']):
                for li in ul.find_all('li'):
                    text = li.get_text(strip=True)
                    # Check if it looks like a skill (not too long, contains tech terms)
                    if (text and len(text) < 100 and 
                        any(keyword in text.lower() for keyword in 
                            ['python', 'sql', 'machine learning', 'data', 'analytics', 'cloud', 'aws', 'azure'])):
                        skills.append(text)
        except Exception as e:
            self.logger.debug(f"Error extracting skills from bullets: {e}")
        return skills

    def extract_with_regex_fallback(self, text: str, field: str) -> Optional[str]:
        """Regex-based extraction as fallback"""
        if not text:
            return None
            
        patterns = {
            'jobType': r'\b(full[- ]?time|part[- ]?time|contract|internship|temporary)\b',
            'pay': r'(?:Salary|Pay)[:\-]?\s*([₹$€£]?\s?[\d,\.]+(?:\s*(?:per|/)?\s*\w+)?)',
            'workLocation': r'Work location[:\-]?\s*([A-Za-z, \-/]+)',
            'benefits': r'Benefits[:\-]?\s*(.+)',
            'schedule': r'Schedule[:\-]?\s*(.+)',
            'education': r'Education[:\-]?\s*(.+)',
        }
        
        pattern = patterns.get(field)
        if pattern:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None

    def fast_extract_text(self, driver: webdriver.Chrome, selectors: list) -> Optional[str]:
        """Optimized text extraction with XPath preference"""
        # Try XPath first (usually faster than CSS)
        for selector in selectors:
            try:
                if selector.startswith('//'):
                    element = driver.find_element(By.XPATH, selector)
                else:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                text = element.text.strip()
                if text:
                    return text
            except:
                continue
        return None

    def extract_all_at_once(self, driver):
        """Extract multiple fields in single DOM query"""
        script = """
        const data = {};
        // Title
        const title = document.querySelector("h1[id*='job-title'], h1[data-test='job-title'], h1");
        data.title = title ? title.textContent.trim() : null;
        // Company  
        const company = document.querySelector(".EmployerProfile_employerNameHeading__bXBYr, [data-test='employer-name']");
        data.company = company ? company.textContent.trim() : null;
        // Location
        const location = document.querySelector("[data-test='location'], .location");
        data.location = location ? location.textContent.trim() : null;
        // Salary
        const salary = document.querySelector("[data-test='detailSalary'], .salary");
        data.salary = salary ? salary.textContent.trim() : null;
        // Job description HTML
        const desc = document.querySelector(".JobDetails_jobDescription__uW_fK, [data-test='jobDescriptionContent']");
        data.job_desc_html = desc ? desc.outerHTML : null;
        return data;
        """
        return driver.execute_script(script)

    def extract_all_at_once_with_retry(self, driver, max_retries=3):
        """Extract multiple fields with retry logic and better fallbacks"""
        for attempt in range(max_retries):
            script = """
            const data = {};
            // Wait for content to be ready
            if (document.readyState !== 'complete') {
                return null;
            }
            // Enhanced selectors with more fallbacks
            const selectors = {
                title: [
                    "h1[id*='job-title']", 
                    "h1[data-test='job-title']", 
                    ".JobDetails_jobTitle__*",
                    "h1.css-*",
                    "h1",
                    ".job-title",
                    "[data-cy='job-title']"
                ],
                company: [
                    ".EmployerProfile_employerNameHeading__bXBYr",
                    "[data-test='employer-name']", 
                    ".EmployerProfile_profileContainer__* h4",
                    ".company-name",
                    "h4[data-test*='company']",
                    ".employer-name"
                ],
                location: [
                    "[data-test='location']",
                    ".JobDetails_location__*",
                    "[data-test='job-location']",
                    ".location",
                    "[data-cy='location']"
                ],
                salary: [
                    "[data-test='detailSalary']",
                    ".JobDetails_salary__*",
                    "[data-test='salary']",
                    ".salary-range",
                    ".pay-range"
                ]
            };
            // Extract with fallback chain
            for (const [field, selectorList] of Object.entries(selectors)) {
                for (const selector of selectorList) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {
                            const text = el.textContent?.trim();
                            if (text && text.length > 0 && !text.match(/^\\s*$/)) {
                                data[field] = text;
                                break;
                            }
                        }
                        if (data[field]) break;
                    } catch (e) {
                        continue;
                    }
                }
            }
            // Job description with better selector
            const descSelectors = [
                ".JobDetails_jobDescription__uW_fK",
                "[data-test='jobDescriptionContent']",
                ".job-description",
                ".description-content"
            ];
            for (const selector of descSelectors) {
                try {
                    const desc = document.querySelector(selector);
                    if (desc && desc.innerHTML.trim()) {
                        data.job_desc_html = desc.outerHTML;
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
            return data;
            """
            try:
                data = driver.execute_script(script)
                if data and data.get('title'):  # Valid extraction
                    return data
            except Exception as e:
                self.logger.warning(f"JS extraction attempt {attempt + 1} failed: {e}")
            # Wait and retry
            if attempt < max_retries - 1:
                time.sleep(1 + attempt)  # Progressive delay
        return {}

    def extract_missing_fields(self, driver, existing_data):
        """Extract any missing critical fields using alternative methods"""
        missing_fields = {}

        # Job type fallback
        if not existing_data.get('job_type'):
            job_type_script = """
                const els = document.querySelectorAll('[class*="type" i], [id*="type" i], [data-test*="type" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length < 100 && /full[- ]?time|part[- ]?time|contract|internship|temporary/i.test(text)) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['job_type'] = driver.execute_script(job_type_script)

        # Work location fallback
        if not existing_data.get('work_location'):
            work_location_script = """
                const els = document.querySelectorAll('[class*="location" i], [id*="location" i], [data-test*="location" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && (text.includes(',') || /remote|hybrid|onsite|office/i.test(text))) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['work_location'] = driver.execute_script(work_location_script)

        # Benefits fallback - Enhanced to look for benefits section
        if not existing_data.get('benefits'):
            benefits_script = """
                // Look for benefits section
                const benefitsHeading = Array.from(document.querySelectorAll('h2, h3')).find(el => 
                    /benefit/i.test(el.textContent) && !/rating/i.test(el.textContent)
                );
                if (benefitsHeading) {
                    // Look for benefits list in the section
                    let section = benefitsHeading.parentElement;
                    while (section && section.tagName !== 'SECTION') {
                        section = section.parentElement;
                    }
                    if (section) {
                        const benefitsList = section.querySelector('ul, ol');
                        if (benefitsList) {
                            return Array.from(benefitsList.querySelectorAll('li')).map(li => li.textContent.trim()).filter(Boolean);
                        }
                    }
                }
                
                // Fallback: look for any elements with "benefit" in class or text
                const els = document.querySelectorAll('[class*="benefit" i], [id*="benefit" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length > 5 && text.length < 500) {
                        return text;
                    }
                }
                return null;
            """
            benefits = driver.execute_script(benefits_script)
            if benefits:
                missing_fields['benefits'] = benefits

        # Schedule fallback
        if not existing_data.get('schedule'):
            schedule_script = """
                const els = document.querySelectorAll('[class*="schedule" i], [id*="schedule" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length > 3 && text.length < 100) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['schedule'] = driver.execute_script(schedule_script)

        # Education fallback - Enhanced to look in job description
        if not existing_data.get('education'):
            education_script = """
                // Look for education in job description
                const descEl = document.querySelector('.JobDetails_jobDescription__uW_fK, [data-test="jobDescriptionContent"]');
                if (descEl) {
                    const text = descEl.textContent;
                    const educationMatch = text.match(/(master['']?s degree|phd|bachelor['']?s degree|education requirements?:?)([^.]+)/i);
                    if (educationMatch) {
                        return educationMatch[0].trim();
                    }
                }
                
                // Fallback: look for education elements
                const els = document.querySelectorAll('[class*="education" i], [id*="education" i]');
                for (const el of els) {
                    const text = el.textContent.trim();
                    if (text && text.length > 3 && text.length < 100) {
                        return text;
                    }
                }
                return null;
            """
            missing_fields['education'] = driver.execute_script(education_script)

        # Most relevant skills fallback - Enhanced
        if not existing_data.get('most_relevant_skills'):
            skills_script = """
                // Look for skills in job description sections
                const descEl = document.querySelector('.JobDetails_jobDescription__uW_fK, [data-test="jobDescriptionContent"]');
                if (descEl) {
                    const skillsSections = ['key competencies', 'what you\\'ll bring', 'requirements', 'qualifications', 'skills'];
                    
                    for (const sectionName of skillsSections) {
                        const section = Array.from(descEl.querySelectorAll('h2, h3, b, strong')).find(el => 
                            el.textContent.toLowerCase().includes(sectionName)
                        );
                        if (section) {
                            let next = section.nextElementSibling;
                            if (next && (next.tagName === 'UL' || next.tagName === 'OL')) {
                                return Array.from(next.querySelectorAll('li')).map(li => li.textContent.trim()).filter(Boolean);
                            }
                            if (next && next.textContent) {
                                return next.textContent.split(/,|\\n/).map(s => s.trim()).filter(Boolean);
                            }
                        }
                    }
                    
                    // Look for bullet points with tech keywords
                    const bullets = descEl.querySelectorAll('ul li, ol li');
                    const skills = [];
                    for (const bullet of bullets) {
                        const text = bullet.textContent.trim();
                        if (text && text.length < 100 && 
                            /python|sql|machine learning|data|analytics|cloud|aws|azure|java|javascript|react|node/i.test(text)) {
                            skills.push(text);
                        }
                    }
                    if (skills.length > 0) {
                        return skills;
                    }
                }
                return null;
            """
            skills = driver.execute_script(skills_script)
            if skills:
                missing_fields['most_relevant_skills'] = skills

        # Other relevant skills fallback - Enhanced
        if not existing_data.get('other_relevant_skills'):
            other_skills_script = """
                // Look for "other relevant skills" or similar sections
                const descEl = document.querySelector('.JobDetails_jobDescription__uW_fK, [data-test="jobDescriptionContent"]');
                if (descEl) {
                    const section = Array.from(descEl.querySelectorAll('h2, h3, b, strong')).find(el => 
                        /other relevant skills|additional skills|nice to have|preferred skills/i.test(el.textContent)
                    );
                    if (section) {
                        let next = section.nextElementSibling;
                        if (next && (next.tagName === 'UL' || next.tagName === 'OL')) {
                            return Array.from(next.querySelectorAll('li')).map(li => li.textContent.trim()).filter(Boolean);
                        }
                        if (next && next.textContent) {
                            return next.textContent.split(/,|\\n/).map(s => s.trim()).filter(Boolean);
                        }
                    }
                }
                return null;
            """
            other_skills = driver.execute_script(other_skills_script)
            if other_skills:
                missing_fields['other_relevant_skills'] = other_skills

        return {k: v for k, v in missing_fields.items() if v}

class HumanBehavior:
    def simulate_human_actions(self, driver):
        # Random mouse movements
        try:
            actions = ActionChains(driver)
            actions.move_by_offset(random.randint(10, 100), random.randint(10, 100))
            actions.perform()
        except Exception:
            pass
        # Random scrolling
        try:
            driver.execute_script(f"window.scrollBy(0, {random.randint(200, 800)});")
            time.sleep(random.uniform(0.5, 2))
        except Exception:
            pass
        # Occasional back/forward
        if random.random() < 0.1:
            try:
                driver.back()
                time.sleep(1)
                driver.forward()
            except Exception:
                pass

class JobDataExtractor:
    def __init__(self):
        self.patterns = {
            'salary_range': r'₹([\d,]+(?:\.\d+)?)[KkLl]?\s*[-–]\s*₹([\d,]+(?:\.\d+)?)[KkLl]?',
            'pay_amount': r'₹([\d,]+(?:\.\d+)?)',
            'job_types': r'Job Types?:\s*([^\n]+)',
            'schedule': r'Schedule:\s*(.*?)(?=\n\w+:|$)',
            'work_location': r'Work Location:\s*([^\n]+)',
            'expected_hours': r'Expected hours:\s*(\d+)\s*per\s*week',
            'experience_years': r'(\d+)\s*years?\s*of.*?experience',
            'education': r'Education(?: requirements)?:\s*([^\n]+)',
            'benefits': r'Benefits?:\s*(.*?)(?=\n\w+:|$)',
        }
    def extract_from_html(self, html_content):
        soup = BeautifulSoup(html_content, 'html.parser')
        job_data = {}
        description_div = soup.find('div', class_='JobDetails_jobDescription__uW_fK')
        if description_div:
            job_data['full_description'] = description_div.get_text(strip=True)
            job_data.update(self._extract_sections(description_div))
        return job_data
    def _extract_sections(self, description_div):
        sections = {}
        text = description_div.get_text()
        for field, pattern in self.patterns.items():
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                if field == 'salary_range':
                    sections['salary_min'] = match.group(1)
                    sections['salary_max'] = match.group(2)
                else:
                    sections[field] = match.group(1).strip()
        return sections
    def extract_with_regex(self, text):
        extracted = {}
        for field, pattern in self.patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                if field == 'salary_range' and len(matches[0]) == 2:
                    extracted['salary_min'] = matches[0][0]
                    extracted['salary_max'] = matches[0][1]
                else:
                    extracted[field] = matches[0] if isinstance(matches[0], str) else matches[0][0]
        return extracted
    def smart_extract(self, content, content_type='html'):
        if content_type == 'html':
            return self.extract_from_html(content)
        else:
            return self.extract_with_regex(content)

# Main Scraper Class
class GlassdoorScraper:
    def __init__(self, config: ScraperConfig = None):
        self.config = config or ScraperConfig()
        self.logger = ScraperLogger()
        self.driver_manager = DriverManager(self.config, self.logger)
        self.field_extractor = FieldExtractor(self.config, self.logger) 
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        self.human_behavior = HumanBehavior()
    
    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, Any]:
        """Main scraping method with monitoring and parallel session support"""
        start_time = time.time()
        metadata = {
            'start_time': datetime.utcnow().isoformat(),
            'job_title': job_title,
            'location': location,
            'requested_jobs': num_jobs,
            'scraped_jobs': 0,
            'failed_jobs': 0,
            'errors': [],
            'failed_urls': []
        }
        
        try:
            driver = self.driver_manager.create_driver()
            try:
                self._perform_search(driver, job_title, location)
                # Over-collect URLs (2x requested) using fast method
                job_urls = self._collect_job_urls_fast(driver, num_jobs * 2)
                self.logger.info(f"Found {len(job_urls)} job URLs")
                
                # Extract jobs, stop when enough valid jobs
                jobs = []
                for i, url in enumerate(job_urls):
                    if len(jobs) >= num_jobs:
                        break
                    
                    self.logger.info(f"Processing job {i+1}/{len(job_urls)}: {url}")
                    
                    # Try to extract job with retry logic
                    job = None
                    for attempt in range(2):  # Try twice
                        try:
                            job = self._extract_current_page_fast(driver, url)
                            if job and job.title:  # Valid job extracted
                                break
                            elif attempt == 0:  # First attempt failed, try detailed extraction
                                self.logger.info(f"Fast extraction failed for {url}, trying detailed extraction")
                                job = self._extract_current_page(driver, url)
                                if job and job.title:
                                    break
                        except Exception as e:
                            self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                            if attempt == 1:  # Last attempt
                                metadata['failed_jobs'] += 1
                                metadata['failed_urls'].append(url)
                                metadata['errors'].append(f"Failed to extract {url}: {str(e)}")
                    
                    if job and job.title:
                        # Coerce string fields if needed
                        if isinstance(job.education, list):
                            job.education = ' '.join([str(e) for e in job.education if e]).strip()
                        if isinstance(job.benefits, list):
                            job.benefits = ' '.join([str(e) for e in job.benefits if e]).strip()
                        if isinstance(job.schedule, list):
                            job.schedule = ' '.join([str(e) for e in job.schedule if e]).strip()
                        if isinstance(job.job_type, list):
                            job.job_type = ' '.join([str(e) for e in job.job_type if e]).strip()
                        
                        jobs.append(job)
                        self.logger.info(f"Successfully extracted job: {job.title}")
                    else:
                        metadata['failed_jobs'] += 1
                        metadata['failed_urls'].append(url)
                        self.logger.warning(f"Failed to extract job from {url}")
                
                metadata.update({
                    'scraped_jobs': len(jobs),
                    'execution_time': time.time() - start_time,
                    'end_time': datetime.utcnow().isoformat(),
                    'success_rate': f"{len(jobs)}/{len(job_urls)} ({len(jobs)/len(job_urls)*100:.1f}%)" if job_urls else "0%"
                })
                
                self.logger.info(f"Successfully scraped {len(jobs)} jobs in {metadata['execution_time']:.2f}s (Success rate: {metadata['success_rate']})")
                
                return {
                    'scraped_jobs': [job.dict() for job in jobs],
                    'metadata': metadata
                }
            finally:
                driver.quit()
        except Exception as e:
            self.logger.error(f"Scraping failed: {str(e)}")
            metadata['errors'].append(str(e))
            metadata['execution_time'] = time.time() - start_time
            metadata['end_time'] = datetime.utcnow().isoformat()
            raise
    
    def _perform_search(self, driver: webdriver.Chrome, job_title: str, location: str):
        """Improved search with better error handling and fallback strategies. Now hits ENTER after filling fields to trigger search."""
        self.logger.info(f"Searching for '{job_title}' in '{location}'")
        def search_action():
            # Strategy 1: Try main search page
            try:
                self.logger.info("Attempting main search page")
                driver.get("https://www.glassdoor.co.in/Job/index.htm")
                time.sleep(5)
                # Check if page loaded
                if "glassdoor" not in driver.current_url.lower():
                    raise Exception("Failed to load Glassdoor main page")
                # Try to find and fill search fields
                try:
                    job_input = driver.find_element(By.ID, "searchBar-jobTitle")
                    location_input = driver.find_element(By.ID, "searchBar-location")
                    # Clear and fill
                    job_input.clear()
                    job_input.send_keys(job_title)
                    time.sleep(1)
                    location_input.clear()
                    location_input.send_keys(location)
                    time.sleep(1)
                    # Hit ENTER to trigger search (matches manual behavior)
                    location_input.send_keys(Keys.RETURN)
                    time.sleep(5)
                    # Check if search worked
                    if "/Job/" in driver.current_url:
                        self.logger.info("Main search successful via ENTER")
                        return True
                    # Fallback: Try clicking the search button if ENTER didn't work
                    search_btn = driver.find_element(By.XPATH, "//button[@type='submit']")
                    driver.execute_script("arguments[0].click();", search_btn)
                    time.sleep(5)
                    if "/Job/" in driver.current_url:
                        self.logger.info("Main search successful via button click fallback")
                        return True
                except Exception as e:
                    self.logger.warning(f"Main search failed: {e}")
            except Exception as e:
                self.logger.warning(f"Main page load failed: {e}")
            # Strategy 2: Direct URL construction
            try:
                self.logger.info("Attempting direct URL construction")
                import urllib.parse
                encoded_title = urllib.parse.quote_plus(job_title)
                search_url = f"https://www.glassdoor.co.in/Job/jobs.htm?sc.keyword={encoded_title}"
                driver.get(search_url)
                time.sleep(8)  # Longer wait
                # Check if we have job listings
                job_elements = driver.find_elements(By.CSS_SELECTOR, "a[data-test='job-title'], .JobCard_jobTitle__GLyJ1")
                if job_elements:
                    self.logger.info("Direct URL search successful")
                    return True
            except Exception as e:
                self.logger.warning(f"Direct URL failed: {e}")
            # Strategy 3: Google redirect method
            try:
                self.logger.info("Attempting Google redirect method")
                google_search = f"site:glassdoor.co.in {job_title} jobs {location}"
                google_url = f"https://www.google.com/search?q={urllib.parse.quote_plus(google_search)}"
                driver.get(google_url)
                time.sleep(3)
                # Look for Glassdoor links
                glassdoor_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'glassdoor.co.in/Job')]")
                if glassdoor_links:
                    glassdoor_links[0].click()
                    time.sleep(5)
                    self.logger.info("Google redirect successful")
                    return True
            except Exception as e:
                self.logger.warning(f"Google redirect failed: {e}")
            # Strategy 4: Alternative domain
            try:
                self.logger.info("Attempting alternative domain")
                alt_url = f"https://www.glassdoor.com/Job/india-{job_title.lower().replace(' ', '-')}-jobs-SRCH_IL.0,5_IN115.htm"
                driver.get(alt_url)
                time.sleep(5)
                job_elements = driver.find_elements(By.CSS_SELECTOR, "a[data-test='job-title']")
                if job_elements:
                    self.logger.info("Alternative domain successful")
                    return True
            except Exception as e:
                self.logger.warning(f"Alternative domain failed: {e}")
            raise RuntimeError("All search strategies failed")
        # Execute with retries
        result = safe_execute_with_retry(
            search_action, 
            retries=3,
            delay=5,
            logger=self.logger
        )
        if not result:
            raise RuntimeError("Search failed after all retries")

    def _collect_job_urls_fast(self, driver: webdriver.Chrome, num_jobs: int) -> list:
        """Faster job URL collection using JavaScript with better error handling"""
        job_urls = set()
        
        # JavaScript to collect all URLs at once with multiple strategies
        collect_script = """
        const urls = new Set();
        
        // Strategy 1: Standard job card links
        const standardSelectors = [
            "a[data-test='job-title']",
            "a.JobCard_jobTitle__GLyJ1", 
            ".JobCard_jobTitle__rw2J1 a",
            "a[href*='/job-listing/']",
            "a[href*='jl=']",
            "[data-test='job-card'] a"
        ];
        
        standardSelectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(el => {
                    const href = el.getAttribute('href');
                    if (href && (href.includes('/job-listing/') || href.includes('jl='))) {
                        urls.add(href.startsWith('http') ? href : 'https://www.glassdoor.co.in' + href);
                    }
                });
            } catch (e) {
                // Continue with next selector
            }
        });
        
        // Strategy 2: Look for any links containing job IDs
        const allLinks = document.querySelectorAll('a[href*="jl="]');
        allLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.includes('jl=')) {
                urls.add(href.startsWith('http') ? href : 'https://www.glassdoor.co.in' + href);
            }
        });
        
        // Strategy 3: Look for job listing URLs in any format
        const jobListingLinks = document.querySelectorAll('a[href*="/job-listing/"]');
        jobListingLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href) {
                urls.add(href.startsWith('http') ? href : 'https://www.glassdoor.co.in' + href);
            }
        });
        
        return Array.from(urls);
        """
        
        # JavaScript to handle popups and modals
        popup_handler_script = """
        // Close authentication modals
        const authSelectors = [
            'button[data-test="closeButton"]',
            '.CloseButton',
            'button[class*="close"]',
            'button[class*="Close"]',
            '[data-test="authModalContainerV2"] button',
            '.authModalContent button',
            'button[aria-label*="close"]',
            'button[aria-label*="Close"]'
        ];
        
        // Close overlay modals
        const overlaySelectors = [
            '[data-test="authModalContainerV2"]',
            '.authModalContent',
            '.modal-overlay',
            '.modal-backdrop',
            '[class*="modal"]',
            '[class*="popup"]',
            '[class*="overlay"]'
        ];
        
        // Try to close modals
        authSelectors.forEach(selector => {
            try {
                const closeBtn = document.querySelector(selector);
                if (closeBtn && closeBtn.offsetParent !== null) {
                    closeBtn.click();
                    return true;
                }
            } catch (e) {
                // Continue with next selector
            }
        });
        
        // Try to remove overlays
        overlaySelectors.forEach(selector => {
            try {
                const overlay = document.querySelector(selector);
                if (overlay && overlay.offsetParent !== null) {
                    overlay.remove();
                    return true;
                }
            } catch (e) {
                // Continue with next selector
            }
        });
        
        // Try to press Escape key
        document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
        
        return false;
        """
        
        max_scrolls = 5  # Reduced from 10
        for scroll in range(max_scrolls):
            try:
                # Handle any popups before scrolling
                self._handle_popups_and_modals(driver)
                
                # Quick scroll and collect
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1)  # Reduced wait time
                
                # Handle popups again after scrolling
                self._handle_popups_and_modals(driver)
                
                # Collect URLs with JavaScript
                urls = driver.execute_script(collect_script)
                if urls:
                    job_urls.update(urls)
                    self.logger.debug(f"Found {len(urls)} URLs on scroll {scroll + 1}")
                
                if len(job_urls) >= num_jobs:
                    break
                
                # Try load more (but don't wait long)
                try:
                    load_more_selectors = [
                        "button[data-test='load-more']",
                        "button[data-test='pagination-footer-next']",
                        "button.css-1gpqj0y",
                        "button[class*='load-more']",
                        "button[class*='pagination']"
                    ]
                    
                    for selector in load_more_selectors:
                        try:
                            load_more = driver.find_element(By.CSS_SELECTOR, selector)
                            if load_more.is_displayed():
                                # Handle popups before clicking
                                self._handle_popups_and_modals(driver)
                                
                                driver.execute_script("arguments[0].click();", load_more)
                                time.sleep(1)  # Minimal wait
                                
                                # Handle popups after clicking
                                self._handle_popups_and_modals(driver)
                                break
                        except:
                            continue
                except Exception as e:
                    self.logger.debug(f"Load more failed on scroll {scroll + 1}: {e}")
                    
            except Exception as e:
                self.logger.warning(f"Error during URL collection on scroll {scroll + 1}: {e}")
                continue
        
        # Clean and validate URLs
        valid_urls = []
        for url in job_urls:
            try:
                # Basic URL validation
                if url and ('glassdoor' in url.lower() or 'jl=' in url):
                    # Remove any fragments or unnecessary parameters
                    clean_url = url.split('#')[0].split('?')[0]
                    if 'jl=' in url:
                        # Keep the job ID parameter
                        clean_url = url.split('#')[0]
                    valid_urls.append(clean_url)
            except Exception as e:
                self.logger.debug(f"Invalid URL {url}: {e}")
        
        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in valid_urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
        
        self.logger.info(f"Collected {len(unique_urls)} unique job URLs")
        return unique_urls[:num_jobs]

    def _extract_jobs_concurrent(self, job_urls: List[str]) -> List[JobPosting]:
        """Extract job details for each job URL in the same browser session (session persistence)."""
        driver = self.driver_manager.create_driver()
        jobs = []
        try:
            for i, url in enumerate(job_urls):
                if i > 0:
                    # Simulate human-like delay and actions
                    time.sleep(random.uniform(3, 7))
                    self.human_behavior.simulate_human_actions(driver)
                job = self._extract_current_page(driver, url)
                if job:
                    jobs.append(job)
        finally:
            driver.quit()
        return jobs

    def _handle_popups_and_modals(self, driver: webdriver.Chrome):
        """Comprehensive popup and modal handling"""
        try:
            # JavaScript to handle various types of popups and modals
            popup_script = """
            // Function to handle all types of popups and modals
            function handlePopups() {
                let closed = false;
                
                // 1. Close authentication modals (Glassdoor specific)
                const authSelectors = [
                    'button[data-test="closeButton"]',
                    '.CloseButton',
                    'button[class*="close"]',
                    'button[class*="Close"]',
                    '[data-test="authModalContainerV2"] button',
                    '.authModalContent button',
                    'button[aria-label*="close"]',
                    'button[aria-label*="Close"]',
                    '.closeButtonWrapper button',
                    'button[type="button"] svg[class*="CloseIcon"]',
                    'button svg[class*="CloseIcon"]'
                ];
                
                authSelectors.forEach(selector => {
                    try {
                        const closeBtn = document.querySelector(selector);
                        if (closeBtn && closeBtn.offsetParent !== null && closeBtn.style.display !== 'none') {
                            closeBtn.click();
                            closed = true;
                            console.log('Closed auth modal with selector:', selector);
                        }
                    } catch (e) {
                        // Continue with next selector
                    }
                });
                
                // 2. Remove modal overlays
                const overlaySelectors = [
                    '[data-test="authModalContainerV2"]',
                    '.authModalContent',
                    '.modal-overlay',
                    '.modal-backdrop',
                    '[class*="modal"]',
                    '[class*="popup"]',
                    '[class*="overlay"]',
                    '.ContentAndBottomSection'
                ];
                
                overlaySelectors.forEach(selector => {
                    try {
                        const overlay = document.querySelector(selector);
                        if (overlay && overlay.offsetParent !== null) {
                            overlay.remove();
                            closed = true;
                            console.log('Removed overlay with selector:', selector);
                        }
                    } catch (e) {
                        // Continue with next selector
                    }
                });
                
                // 3. Try to press Escape key
                document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
                
                // 4. Remove any fixed positioned elements that might be blocking
                const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
                fixedElements.forEach(el => {
                    try {
                        const style = window.getComputedStyle(el);
                        if (style.zIndex > 1000) { // High z-index elements are usually modals
                            el.remove();
                            closed = true;
                            console.log('Removed fixed element with high z-index');
                        }
                    } catch (e) {
                        // Continue with next element
                    }
                });
                
                // 5. Enable scrolling if disabled
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                return closed;
            }
            
            return handlePopups();
            """
            
            # Execute popup handling
            popup_closed = driver.execute_script(popup_script)
            
            if popup_closed:
                self.logger.info("Successfully handled popup/modal")
                time.sleep(0.5)  # Brief pause after closing popup
                
        except Exception as e:
            self.logger.debug(f"Error handling popups: {e}")
    
    def _expand_show_more_aggressive(self, driver):
        """More aggressive show more expansion with popup handling"""
        show_more_selectors = [
            "button[data-test='show-more-cta']",
            "button[data-test='show-more']", 
            "button.css-1gpqj0y",
            "[class*='show-more']",
            "button:contains('Show more')",
            "button:contains('Read more')"
        ]
        
        for selector in show_more_selectors:
            try:
                if selector.startswith("button:contains"):
                    # XPath for text content
                    text_content = selector.split('(')[1].split(')')[0].strip("'")
                    xpath = f"//button[contains(text(), '{text_content}')]"
                    show_more = driver.find_element(By.XPATH, xpath)
                else:
                    show_more = driver.find_element(By.CSS_SELECTOR, selector)
                    
                if show_more.is_displayed() and show_more.is_enabled():
                    # Handle popups before clicking
                    self._handle_popups_and_modals(driver)
                    
                    driver.execute_script("arguments[0].click();", show_more)
                    time.sleep(1)
                    
                    # Handle popups after clicking
                    self._handle_popups_and_modals(driver)
                    break
            except Exception:
                continue

    def _expand_show_more(self, driver):
        """Click the 'Show more' button if present to expand the full job description."""
        self._expand_show_more_aggressive(driver)

    def _extract_company_logo(self, driver):
        """Extract company logo URL from the header section."""
        selectors = [
            ".EmployerProfile_profileContainer__63w3R img",
            ".EmployerProfile_logo__3xqON img",
            "img[alt*='logo']",
            "img.avatar-base_Image__2RcF9"
        ]
        for selector in selectors:
            try:
                img = driver.find_element(By.CSS_SELECTOR, selector)
                src = img.get_attribute("src")
                if src and src.startswith("http"):
                    return src
            except Exception:
                continue
        return None

    def _extract_current_page_fast(self, driver, job_url: str) -> Optional[JobPosting]:
        """Optimized extraction with reduced waits and comprehensive error handling"""
        try:
            driver.get(job_url)
            
            # Handle any popups that might appear when loading the page
            self._handle_popups_and_modals(driver)
            
            # Wait for page to load with multiple fallback strategies
            page_loaded = False
            try:
                # Strategy 1: Wait for title element
                WebDriverWait(driver, 8).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.TAG_NAME, "h1")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test='job-title']")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".JobDetails_jobDetailsHeader__Hd9M3")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='job-title']"))
                    )
                )
                page_loaded = True
            except TimeoutException:
                self.logger.warning(f"Timeout waiting for page elements on {job_url}")
                
                # Strategy 2: Check if page has any content
                try:
                    page_text = driver.find_element(By.TAG_NAME, "body").text
                    if len(page_text) > 100:  # Page has some content
                        page_loaded = True
                        self.logger.info(f"Page loaded with fallback check for {job_url}")
                except:
                    pass
            
            if not page_loaded:
                self.logger.error(f"Page failed to load for {job_url}")
                return None
            
            # Handle popups again after page load
            self._handle_popups_and_modals(driver)
            
            # Aggressive show more expansion
            self._expand_show_more_aggressive(driver)
            
            # Handle popups after expanding content
            self._handle_popups_and_modals(driver)
            
            # Extract all data at once with JavaScript (with retry)
            data = self.field_extractor.extract_all_at_once_with_retry(driver)
            
            # Fallback extraction if main extraction fails
            if not data or not data.get('title'):
                self.logger.warning(f"Main extraction failed for {job_url}, trying fallback")
                data = self._fallback_extraction(driver)
            
            if not data or not data.get('title'):
                self.logger.error(f"All extraction methods failed for {job_url}")
                return None
            
            # Field-specific fallback extraction for missing fields
            missing_fields = self.field_extractor.extract_missing_fields(driver, data)
            data.update(missing_fields)
            
            # Process job description if available
            job_desc = None
            extra_sections = {}
            if data.get('job_desc_html'):
                try:
                    # Use your existing parsing logic but with the pre-fetched HTML
                    soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                    extra_sections = self.field_extractor.extract_job_description_sections(soup)
                    # Quick markdown generation
                    if extra_sections:
                        job_desc = self._quick_markdown_generation(extra_sections)
                except Exception as e:
                    self.logger.debug(f"Job description parsing failed: {e}")
            
            # Quick easy apply check with multiple selectors
            easy_apply = False
            try:
                easy_apply_selectors = [
                    "button[data-test='easyApply']",
                    "button[data-test='apply-button']",
                    "button[data-test='applyButton']",
                    "button.css-1n6j6mr",
                    ".JobDetails_applyButtonContainer__L36Bs button"
                ]
                for selector in easy_apply_selectors:
                    try:
                        easy_apply_btn = driver.find_element(By.CSS_SELECTOR, selector)
                        if easy_apply_btn.is_displayed():
                            easy_apply = True
                            break
                    except:
                        continue
            except Exception as e:
                self.logger.debug(f"Easy apply check failed: {e}")
            
            # Create JobPosting with comprehensive field mapping
            return JobPosting(
                title=data.get('title', ''),
                company_name=data.get('company'),
                location=data.get('location'),
                salary=data.get('salary'),
                job_type=data.get('job_type'),
                work_location=data.get('work_location'),
                benefits=data.get('benefits'),
                schedule=data.get('schedule'),
                education=data.get('education'),
                most_relevant_skills=data.get('most_relevant_skills', []),
                other_relevant_skills=data.get('other_relevant_skills', []),
                easy_apply=easy_apply,
                job_description=job_desc,
                extra_sections=extra_sections,
                job_id=self._extract_job_id(job_url),
                jd_url=job_url,
            )
        except Exception as e:
            self.logger.error(f"Fast extraction failed for {job_url}: {str(e)}")
            return None
    
    def _fallback_extraction(self, driver) -> Dict[str, Any]:
        """Fallback extraction method when main extraction fails"""
        fallback_data = {}
        try:
            # Try to extract basic fields using multiple strategies
            fallback_script = """
            const data = {};
            
            // Title extraction with multiple selectors
            const titleSelectors = [
                'h1[id*="job-title"]',
                'h1[data-test="job-title"]',
                '.JobDetails_jobDetailsHeader__Hd9M3 h1',
                'h1',
                '[class*="job-title"]',
                '[data-test*="title"]'
            ];
            
            for (const selector of titleSelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim()) {
                    data.title = el.textContent.trim();
                    break;
                }
            }
            
            // Company extraction
            const companySelectors = [
                '.EmployerProfile_employerNameHeading__bXBYr h4',
                '[data-test="employer-name"]',
                '.EmployerProfile_profileContainer__* h4',
                'h4',
                '[class*="company"]',
                '[class*="employer"]'
            ];
            
            for (const selector of companySelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim() && el.textContent.trim().length < 100) {
                    data.company = el.textContent.trim();
                    break;
                }
            }
            
            // Location extraction
            const locationSelectors = [
                '[data-test="location"]',
                '.JobDetails_locationAndPay__XGFmY div',
                '[class*="location"]',
                '[data-test*="location"]'
            ];
            
            for (const selector of locationSelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim() && (el.textContent.includes(',') || el.textContent.length < 50)) {
                    data.location = el.textContent.trim();
                    break;
                }
            }
            
            // Salary extraction
            const salarySelectors = [
                '[data-test="detailSalary"]',
                '.JobCard_salaryEstimate__QpbTW',
                '[class*="salary"]',
                '[data-test*="salary"]'
            ];
            
            for (const selector of salarySelectors) {
                const el = document.querySelector(selector);
                if (el && el.textContent.trim()) {
                    data.salary = el.textContent.trim();
                    break;
                }
            }
            
            // Job description extraction
            const descSelectors = [
                '.JobDetails_jobDescription__uW_fK',
                '[data-test="jobDescriptionContent"]',
                '.job-description',
                '[class*="description"]'
            ];
            
            for (const selector of descSelectors) {
                const el = document.querySelector(selector);
                if (el && el.innerHTML.trim()) {
                    data.job_desc_html = el.outerHTML;
                    break;
                }
            }
            
            return data;
            """
            
            fallback_data = driver.execute_script(fallback_script)
            
        except Exception as e:
            self.logger.debug(f"Fallback extraction failed: {e}")
        
        return fallback_data

    def _quick_markdown_generation(self, sections: dict) -> str:
        """Fast markdown generation without heavy processing"""
        md_parts = []
        for section, content in sections.items():
            if content:
                md_parts.append(f"### {section.title()}")
                if isinstance(content, list):
                    md_parts.extend(f"- {item}" for item in content[:5])  # Limit items
                else:
                    md_parts.append(str(content)[:500])  # Limit length
        return "\n".join(md_parts)

    def _extract_job_id(self, url: str) -> Optional[str]:
        """Extract job ID from URL"""
        patterns = [r'jl=(\d+)', r'jobListingId=(\d+)', r'/(\d+)\.htm']
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None

    def scrape_jobs_parallel(self, job_title: str, location: str, num_jobs: int = 5) -> dict:
        """
        Parallel scraping using multiple processes. Collects job URLs in the main process, then scrapes job details in parallel.
        Returns the same structure as scrape_jobs.
        """
        start_time = time.time()
        metadata = {
            'start_time': datetime.utcnow().isoformat(),
            'job_title': job_title,
            'location': location,
            'requested_jobs': num_jobs,
            'scraped_jobs': 0,
            'failed_jobs': 0,
            'errors': [],
            'failed_urls': []
        }
        jobs = []
        
        try:
            # Step 1: Collect job URLs in main process
            driver = self.driver_manager.create_driver()
            try:
                self._perform_search(driver, job_title, location)
                # Collect exactly the number of URLs requested, not 2x
                job_urls = self._collect_job_urls_fast(driver, num_jobs)
                self.logger.info(f"Found {len(job_urls)} job URLs for parallel scraping (requested: {num_jobs})")
            finally:
                driver.quit()
                
            if not job_urls:
                metadata['errors'].append('No job URLs found')
                metadata['execution_time'] = time.time() - start_time
                metadata['end_time'] = datetime.utcnow().isoformat()
                return {'scraped_jobs': [], 'metadata': metadata}
            
            # Step 2: Use ThreadPoolExecutor instead of ProcessPoolExecutor for better reliability
            # Split URLs into smaller chunks to avoid memory issues
            max_workers = min(3, len(job_urls))  # Max 3 threads to avoid detection
            chunk_size = max(1, ceil(len(job_urls) / max_workers))
            url_chunks = [job_urls[i:i + chunk_size] for i in range(0, len(job_urls), chunk_size)]
            
            self.logger.info(f"Processing {len(job_urls)} URLs in {len(url_chunks)} chunks with {max_workers} workers")
            
            # Step 3: Scrape in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=max_workers) as pool:
                futures = []
                for i, chunk in enumerate(url_chunks):
                    # Stagger the start of each thread to avoid simultaneous requests
                    self.logger.info(f"Submitting chunk {i+1}/{len(url_chunks)} with {len(chunk)} URLs")
                    future = pool.submit(self._scrape_url_chunk_threaded, chunk, i * 3)
                    futures.append(future)
                
                # Collect results with timeout
                completed_chunks = 0
                for future in as_completed(futures, timeout=300):  # 5 minute timeout
                    try:
                        completed_chunks += 1
                        self.logger.info(f"Processing completed chunk {completed_chunks}/{len(url_chunks)}")
                        chunk_jobs = future.result(timeout=60)  # 1 minute per chunk
                        
                        valid_jobs = 0
                        for job in chunk_jobs:
                            if job and job.get('title'):
                                jobs.append(job)
                                valid_jobs += 1
                                if len(jobs) >= num_jobs:  # Stop when we have enough
                                    self.logger.info(f"Reached target of {num_jobs} jobs, stopping processing")
                                    break
                            else:
                                metadata['failed_jobs'] += 1
                        
                        self.logger.info(f"Chunk {completed_chunks} completed: {valid_jobs} valid jobs, {len(chunk_jobs) - valid_jobs} failed")
                        
                        if len(jobs) >= num_jobs:  # Stop processing if we have enough
                            break
                    except Exception as e:
                        self.logger.error(f"Chunk {completed_chunks} processing failed: {str(e)}")
                        metadata['errors'].append(str(e))
            
            # Limit to requested number of jobs
            jobs = jobs[:num_jobs]
            
            metadata.update({
                'scraped_jobs': len(jobs),
                'execution_time': time.time() - start_time,
                'end_time': datetime.utcnow().isoformat(),
                'success_rate': f"{len(jobs)}/{len(job_urls)} ({len(jobs)/len(job_urls)*100:.1f}%)" if job_urls else "0%"
            })
            
            self.logger.info(f"Parallel scraping completed: {len(jobs)} jobs in {metadata['execution_time']:.2f}s")
            return {'scraped_jobs': jobs, 'metadata': metadata}
            
        except Exception as e:
            self.logger.error(f"Parallel scraping failed: {str(e)}")
            metadata['errors'].append(str(e))
            metadata['execution_time'] = time.time() - start_time
            metadata['end_time'] = datetime.utcnow().isoformat()
            return {'scraped_jobs': jobs, 'metadata': metadata}

    def _scrape_url_chunk_threaded(self, urls: list, delay_seconds: int = 0) -> list:
        """
        Thread-safe helper for parallel scraping: launches a new driver, scrapes all jobs in the chunk, returns list of dicts.
        """
        # Wait before starting to stagger requests
        if delay_seconds > 0:
            time.sleep(delay_seconds)
        
        # Enhanced anti-detection config for worker
        chrome_driver_path = '/Users/<USER>/Desktop/glass/chromedriver'
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--headless')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values": {
                "images": 2, 
                "media_stream": 2, 
                "stylesheets": 1,
                "javascript": 1  # Keep JS enabled for extraction
            },
            "profile.managed_default_content_settings": {"images": 2}
        })
        
        # Random user agent
        user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        ]
        options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        service = Service(chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        
        # Execute stealth script
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        results = []
        try:
            for i, url in enumerate(urls):
                try:
                    # Add random delay between requests
                    if i > 0:
                        time.sleep(random.uniform(2, 4))  # Reduced delay for faster processing
                    
                    driver.get(url)
                    
                    # Wait for page load with multiple selectors
                    WebDriverWait(driver, 8).until(  # Reduced timeout
                        EC.any_of(
                            EC.presence_of_element_located((By.TAG_NAME, "h1")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test='job-title']")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".JobDetails_jobDetailsHeader__Hd9M3")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='job-title']")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "body"))
                        )
                    )
                    
                    # Check for anti-bot page
                    page_text = driver.page_source.lower()
                    if "help us protect glassdoor" in page_text or "blocked" in page_text:
                        results.append({
                            'title': None, 
                            'error': 'Blocked by Glassdoor protection', 
                            'jd_url': url,
                            'company': None,
                            'job_description': None
                        })
                        continue
                    
                    # Expand show more button if present
                    try:
                        show_more_selectors = [
                            "button[data-test='show-more-cta']",
                            "button[data-test='show-more']", 
                            "button.css-1gpqj0y",
                            "[class*='show-more']",
                            "button:contains('Show more')",
                            "button:contains('Read more')"
                        ]
                        
                        for selector in show_more_selectors:
                            try:
                                if selector.startswith("button:contains"):
                                    text_content = selector.split('(')[1].split(')')[0].strip("'")
                                    xpath = f"//button[contains(text(), '{text_content}')]"
                                    show_more = driver.find_element(By.XPATH, xpath)
                                else:
                                    show_more = driver.find_element(By.CSS_SELECTOR, selector)
                                    
                                if show_more.is_displayed() and show_more.is_enabled():
                                    driver.execute_script("arguments[0].click();", show_more)
                                    time.sleep(1)
                                    break
                            except Exception:
                                continue
                    except Exception:
                        pass
                    
                    # Use the same sophisticated extraction as the simple endpoint
                    data = driver.execute_script('''
                        const data = {};
                        
                        // Title extraction with multiple selectors
                        const titleSelectors = [
                            "h1[id*='job-title']", 
                            "h1[data-test='job-title']", 
                            "h1.css-1qaijid",
                            ".JobDetails_jobTitle__Nw_N2",
                            "h1"
                        ];
                        for (let selector of titleSelectors) {
                            const title = document.querySelector(selector);
                            if (title && title.textContent.trim()) {
                                data.title = title.textContent.trim();
                                break;
                            }
                        }
                        
                        // Company extraction
                        const companySelectors = [
                            ".EmployerProfile_employerNameHeading__bXBYr",
                            "[data-test='employer-name']",
                            ".EmployerProfile_profileContainer__d6vLt h4",
                            "h4"
                        ];
                        for (let selector of companySelectors) {
                            const company = document.querySelector(selector);
                            if (company && company.textContent.trim()) {
                                data.company = company.textContent.trim();
                                break;
                            }
                        }
                        
                        // Location extraction
                        const locationSelectors = [
                            "[data-test='location']",
                            ".JobDetails_location__mSg5h",
                            "[data-test='job-location']"
                        ];
                        for (let selector of locationSelectors) {
                            const location = document.querySelector(selector);
                            if (location && location.textContent.trim()) {
                                data.location = location.textContent.trim();
                                break;
                            }
                        }
                        
                        // Salary extraction
                        const salarySelectors = [
                            "[data-test='detailSalary']",
                            ".JobDetails_salary__6VyJK",
                            "[data-test='salary']"
                        ];
                        for (let selector of salarySelectors) {
                            const salary = document.querySelector(selector);
                            if (salary && salary.textContent.trim()) {
                                data.salary = salary.textContent.trim();
                                break;
                            }
                        }
                        
                        // Job description extraction
                        const descSelectors = [
                            ".JobDetails_jobDescription__uW_fK",
                            ".JobDetails_jobDescription__6VeBn",
                            "[data-test='jobDescriptionContent']",
                            ".job-description"
                        ];
                        for (let selector of descSelectors) {
                            const desc = document.querySelector(selector);
                            if (desc && desc.innerHTML.trim()) {
                                data.job_desc_html = desc.outerHTML;
                                break;
                            }
                        }
                        
                        // Check for easy apply button
                        const easyApplySelectors = [
                            "button[data-test='easyApply']",
                            "button[data-test='apply-button']",
                            "button[data-test='applyButton']",
                            "button.css-1n6j6mr",
                            ".JobDetails_applyButtonContainer__L36Bs button"
                        ];
                        data.easy_apply = false;
                        for (let selector of easyApplySelectors) {
                            const btn = document.querySelector(selector);
                            if (btn && btn.offsetParent !== null) { // Check if visible
                                data.easy_apply = true;
                                break;
                            }
                        }
                        
                        return data;
                    ''')
                    
                    # Validate that we got basic data
                    if not data or not data.get('title'):
                        results.append({
                            'title': None, 
                            'error': 'Failed to extract job title', 
                            'jd_url': url,
                            'company': None,
                            'job_description': None
                        })
                        continue
                    
                    # Process job description using the same sophisticated logic as simple endpoint
                    job_desc = None
                    extra_sections = {}
                    if data.get('job_desc_html'):
                        try:
                            soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                            
                            # Use the same section extraction logic as the simple endpoint
                            sections = {}
                            desc_div = soup.find("div", class_="JobDetails_jobDescription__uW_fK")
                            
                            if not desc_div:
                                # Try fallback selectors
                                for selector in ['.JobDetails_jobDescription__6VeBn', '[data-test="jobDescriptionContent"]']:
                                    desc_div = soup.select_one(selector.replace('div.', '').replace('div', ''))
                                    if desc_div:
                                        break
                            
                            if desc_div:
                                # Process HTML structure using the same logic as simple endpoint
                                current_section = None
                                current_content = []
                                
                                for element in desc_div.descendants:
                                    if hasattr(element, 'name'):
                                        if element.name in ['h1', 'h2', 'h3', 'h4', 'b', 'strong']:
                                            # Save previous section
                                            if current_section and current_content:
                                                # Clean section name and avoid conflicts with main fields
                                                clean_section = current_section.lower().strip()
                                                if clean_section not in ['job title', 'job location', 'shift', 'qualification', 'salary', 'pay']:
                                                    sections[clean_section] = current_content
                                            
                                            # Start new section
                                            section_text = element.get_text(strip=True).lower().rstrip(':')
                                            current_section = section_text
                                            current_content = []
                                            
                                        elif element.name in ['p', 'li', 'div'] and current_section:
                                            text = element.get_text(strip=True)
                                            if text and len(text) > 3:  # Filter out very short text
                                                # Clean up common artifacts
                                                cleaned_text = text
                                                # Remove website URLs and artifacts
                                                cleaned_text = re.sub(r'\b\w+\.com\+\d+\b', '', cleaned_text)
                                                cleaned_text = re.sub(r'\b\w+\.org\+\d+\b', '', cleaned_text)
                                                cleaned_text = re.sub(r'\b\w+\.in\+\d+\b', '', cleaned_text)
                                                cleaned_text = re.sub(r'\b\w+\.net\+\d+\b', '', cleaned_text)
                                                # Remove standalone URLs
                                                cleaned_text = re.sub(r'https?://[^\s]+', '', cleaned_text)
                                                # Remove extra whitespace and punctuation artifacts
                                                cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
                                                cleaned_text = re.sub(r'[^\w\s\-.,!?;:()]', '', cleaned_text)
                                                cleaned_text = cleaned_text.strip()
                                                
                                                if cleaned_text and len(cleaned_text) > 5:
                                                    current_content.append(cleaned_text)
                                
                                # Save last section
                                if current_section and current_content:
                                    clean_section = current_section.lower().strip()
                                    if clean_section not in ['job title', 'job location', 'shift', 'qualification', 'salary', 'pay']:
                                        sections[clean_section] = current_content
                                
                                # Extract specific fields from sections (avoid conflicts)
                                for section_name, content in sections.items():
                                    if 'benefit' in section_name and not data.get('benefits'):
                                        data['benefits'] = content
                                    elif 'schedule' in section_name and not data.get('schedule'):
                                        data['schedule'] = content
                                    elif 'education' in section_name or 'qualification' in section_name:
                                        if not data.get('education'):
                                            data['education'] = content
                                    elif 'skill' in section_name or 'technology' in section_name:
                                        if not data.get('other_relevant_skills'):
                                            data['other_relevant_skills'] = content
                                    elif 'responsibility' in section_name:
                                        if not data.get('responsibilities'):
                                            data['responsibilities'] = content
                                    elif 'requirement' in section_name or 'qualification' in section_name:
                                        if not data.get('requirements'):
                                            data['requirements'] = content
                                
                                # Store sections for extra_sections (but clean them up)
                                extra_sections = {}
                                for section_name, content in sections.items():
                                    # Skip sections that are already mapped to main fields
                                    if section_name not in ['benefits', 'schedule', 'education', 'skills', 'responsibilities', 'requirements']:
                                        # Clean up the content to avoid duplicates
                                        if isinstance(content, list):
                                            # Remove duplicates and very short items
                                            cleaned_content = []
                                            seen_items = set()
                                            for item in content:
                                                clean_item = item.strip()
                                                # Additional cleaning for artifacts
                                                clean_item = re.sub(r'\b\w+\.com\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'\b\w+\.org\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'\b\w+\.in\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'\b\w+\.net\+\d+\b', '', clean_item)
                                                clean_item = re.sub(r'https?://[^\s]+', '', clean_item)
                                                clean_item = re.sub(r'\s+', ' ', clean_item)
                                                clean_item = clean_item.strip()
                                                
                                                if clean_item and len(clean_item) > 5 and clean_item not in seen_items:
                                                    cleaned_content.append(clean_item)
                                                    seen_items.add(clean_item)
                                            if cleaned_content:
                                                extra_sections[section_name] = cleaned_content
                                        else:
                                            # Clean single content items too
                                            if isinstance(content, str):
                                                clean_content = re.sub(r'\b\w+\.com\+\d+\b', '', content)
                                                clean_content = re.sub(r'\b\w+\.org\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.in\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.net\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'https?://[^\s]+', '', clean_content)
                                                clean_content = re.sub(r'\s+', ' ', clean_content)
                                                clean_content = clean_content.strip()
                                                if clean_content and len(clean_content) > 5:
                                                    extra_sections[section_name] = clean_content
                                            else:
                                                extra_sections[section_name] = content
                                
                                # Generate clean markdown using the same logic as simple endpoint
                                if sections:
                                    md_parts = []
                                    for section, content in sections.items():
                                        if content:
                                            md_parts.append(f"### {section.title()}")
                                            if isinstance(content, list):
                                                # Clean each item before adding to markdown
                                                clean_items = []
                                                for item in content[:5]:  # Limit items
                                                    clean_item = re.sub(r'\b\w+\.com\+\d+\b', '', str(item))
                                                    clean_item = re.sub(r'\b\w+\.org\+\d+\b', '', clean_item)
                                                    clean_item = re.sub(r'\b\w+\.in\+\d+\b', '', clean_item)
                                                    clean_item = re.sub(r'\b\w+\.net\+\d+\b', '', clean_item)
                                                    clean_item = re.sub(r'https?://[^\s]+', '', clean_item)
                                                    clean_item = re.sub(r'\s+', ' ', clean_item)
                                                    clean_item = clean_item.strip()
                                                    if clean_item and len(clean_item) > 5:
                                                        clean_items.append(clean_item)
                                                md_parts.extend(f"- {item}" for item in clean_items)
                                            else:
                                                # Clean single content
                                                clean_content = re.sub(r'\b\w+\.com\+\d+\b', '', str(content))
                                                clean_content = re.sub(r'\b\w+\.org\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.in\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'\b\w+\.net\+\d+\b', '', clean_content)
                                                clean_content = re.sub(r'https?://[^\s]+', '', clean_content)
                                                clean_content = re.sub(r'\s+', ' ', clean_content)
                                                clean_content = clean_content.strip()
                                                if clean_content and len(clean_content) > 10:
                                                    md_parts.append(clean_content[:500])  # Limit length
                                    job_desc = "\n".join(md_parts)
                                else:
                                    # Fallback: convert HTML to simple text
                                    job_desc = soup.get_text(separator='\n', strip=True)[:1000]
                            
                        except Exception as e:
                            # If parsing fails, convert HTML to simple text
                            try:
                                soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                                job_desc = soup.get_text(separator='\n', strip=True)[:1000]
                            except:
                                job_desc = "Job description not available"
                    
                    # Extract additional fields using the same logic as simple endpoint
                    if data.get('job_desc_html'):
                        try:
                            soup = BeautifulSoup(data['job_desc_html'], 'html.parser')
                            desc_text = soup.get_text(separator=' ').lower()
                            
                            # Extract job type (only if not already set)
                            if not data.get('job_type'):
                                if 'full-time' in desc_text or 'full time' in desc_text:
                                    data['job_type'] = 'Full-time'
                                elif 'part-time' in desc_text or 'part time' in desc_text:
                                    data['job_type'] = 'Part-time'
                                elif 'contract' in desc_text:
                                    data['job_type'] = 'Contract'
                                elif 'internship' in desc_text:
                                    data['job_type'] = 'Internship'
                            
                            # Extract work location (only if not already set)
                            if not data.get('work_location'):
                                if 'remote' in desc_text or 'work from home' in desc_text:
                                    data['work_location'] = 'Remote'
                                elif 'hybrid' in desc_text:
                                    data['work_location'] = 'Hybrid'
                                elif 'on-site' in desc_text or 'onsite' in desc_text or 'in person' in desc_text:
                                    data['work_location'] = 'On-site'
                            
                            # Extract skills using the same logic as simple endpoint (only if not already set)
                            if not data.get('most_relevant_skills'):
                                skills = []
                                skill_keywords = ['python', 'java', 'javascript', 'sql', 'machine learning', 'ai', 'data science', 'react', 'node.js', 'aws', 'azure', 'docker', 'kubernetes', 'html', 'css', 'git', 'agile', 'scrum']
                                for skill in skill_keywords:
                                    if skill in desc_text:
                                        skills.append(skill)
                                data['most_relevant_skills'] = skills
                            
                        except Exception as e:
                            pass
                    
                    # Extract job ID from URL
                    job_id_match = re.search(r'jl=(\d+)', url)
                    if job_id_match:
                        data['job_id'] = job_id_match.group(1)
                    
                    # Clean up data and ensure proper structure
                    cleaned_data = {
                        'title': data.get('title', ''),
                        'company_name': data.get('company'),
                        'location': data.get('location'),
                        'salary': data.get('salary'),
                        'job_type': data.get('job_type'),
                        'work_location': data.get('work_location'),
                        'benefits': data.get('benefits'),
                        'schedule': data.get('schedule'),
                        'education': data.get('education'),
                        'most_relevant_skills': data.get('most_relevant_skills', []),
                        'other_relevant_skills': data.get('other_relevant_skills', []),
                        'easy_apply': data.get('easy_apply', False),
                        'job_description': job_desc,
                        'extra_sections': extra_sections,
                        'job_id': data.get('job_id'),
                        'jd_url': url,
                    }
                    
                    # Clean up list fields and ensure they're strings
                    for field in ['benefits', 'schedule', 'education']:
                        if isinstance(cleaned_data[field], list):
                            cleaned_data[field] = ' '.join([str(item) for item in cleaned_data[field] if item]).strip()
                        elif cleaned_data[field] is None:
                            cleaned_data[field] = None
                    
                    # Ensure skills are lists and clean them
                    for field in ['most_relevant_skills', 'other_relevant_skills']:
                        if not isinstance(cleaned_data[field], list):
                            if cleaned_data[field]:
                                cleaned_data[field] = [str(cleaned_data[field])]
                            else:
                                cleaned_data[field] = []
                        else:
                            # Clean skills list
                            clean_skills = []
                            for skill in cleaned_data[field]:
                                if isinstance(skill, str):
                                    # Clean skill text
                                    clean_skill = re.sub(r'\b\w+\.com\+\d+\b', '', skill)
                                    clean_skill = re.sub(r'\b\w+\.org\+\d+\b', '', clean_skill)
                                    clean_skill = re.sub(r'\b\w+\.in\+\d+\b', '', clean_skill)
                                    clean_skill = re.sub(r'\b\w+\.net\+\d+\b', '', clean_skill)
                                    clean_skill = re.sub(r'https?://[^\s]+', '', clean_skill)
                                    clean_skill = re.sub(r'\s+', ' ', clean_skill)
                                    clean_skill = clean_skill.strip()
                                    if clean_skill and len(clean_skill) > 2:
                                        clean_skills.append(clean_skill)
                            cleaned_data[field] = clean_skills
                    
                    # Final cleanup: remove any duplicate or conflicting data from extra_sections
                    if cleaned_data.get('extra_sections'):
                        final_extra_sections = {}
                        main_fields = ['title', 'company_name', 'location', 'salary', 'job_type', 'work_location', 'benefits', 'schedule', 'education']
                        
                        for section_name, content in cleaned_data['extra_sections'].items():
                            # Skip sections that might conflict with main fields
                            if section_name.lower() not in [field.lower() for field in main_fields]:
                                # Also skip very generic section names
                                if section_name.lower() not in ['job', 'location', 'shift', 'qualification', 'pay']:
                                    final_extra_sections[section_name] = content
                        
                        cleaned_data['extra_sections'] = final_extra_sections
                    
                    # Remove any None values that might cause issues
                    cleaned_data = {k: v for k, v in cleaned_data.items() if v is not None or k in ['title', 'jd_url']}
                    
                    results.append(cleaned_data)
                    
                except Exception as e:
                    results.append({
                        'title': None, 
                        'error': str(e), 
                        'jd_url': url,
                        'company_name': None,
                        'job_description': None
                    })
        finally:
            driver.quit()
        return results

    @staticmethod
    def _scrape_url_chunk(urls: list) -> list:
        """
        Legacy helper for parallel scraping: launches a new driver, scrapes all jobs in the chunk, returns list of dicts.
        """
        return GlassdoorScraper._scrape_url_chunk_with_delay(urls, 0)

# --- FastAPI endpoint and CLI entry point ---

app = FastAPI()

# Define allowed origins including ngrok URLs
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001",
    "https://localhost:3001",
    "https://0305-103-247-7-151.ngrok-free.app",
    "https://7ea9-103-247-7-151.ngrok-free.app",
    "https://65d0-202-148-58-240.ngrok-free.app",
    "*"  # Allow all origins for development
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,  # Set to False when using "*" or multiple origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

scraper_instance = GlassdoorScraper()

# Request model for POST endpoint
class GlassdoorRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = 5

@app.get("/scrape_jobs")
def scrape_jobs_api(job_title: str = Query(...), location: str = Query(...), num_jobs: int = Query(5, ge=1, le=50)):
    """API endpoint to scrape Glassdoor jobs (GET)"""
    result = scraper_instance.scrape_jobs(job_title, location, num_jobs)
    return JSONResponse(content=result)

@app.post("/scrape_jobs")
def scrape_jobs_post_api(request: GlassdoorRequest):
    """API endpoint to scrape Glassdoor jobs (POST)"""
    result = scraper_instance.scrape_jobs(request.job_title, request.location, request.num_jobs)
    return JSONResponse(content=result)

@app.options("/scrape_jobs")
async def options_scrape_jobs():
    return {"message": "OK"}

@app.options("/scrape_jobs")
async def options_scrape_jobs_post():
    return {"message": "OK"}

@app.get("/scrape_jobs_parallel")
def scrape_jobs_parallel_api(job_title: str = Query(...), location: str = Query(...), num_jobs: int = Query(5, ge=1, le=50)):
    """
    API endpoint to scrape Glassdoor jobs in parallel (GET).
    """
    try:
        result = scraper_instance.scrape_jobs_parallel(job_title, location, num_jobs)
        return JSONResponse(content=result)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": f"Parallel scraping failed: {str(e)}",
                "scraped_jobs": [],
                "metadata": {
                    "error": str(e),
                    "requested_jobs": num_jobs,
                    "scraped_jobs": 0
                }
            }
        )

@app.post("/scrape_jobs_parallel")
def scrape_jobs_parallel_post_api(request: GlassdoorRequest):
    """
    API endpoint to scrape Glassdoor jobs in parallel (POST).
    """
    try:
        result = scraper_instance.scrape_jobs_parallel(request.job_title, request.location, request.num_jobs)
        return JSONResponse(content=result)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": f"Parallel scraping failed: {str(e)}",
                "scraped_jobs": [],
                "metadata": {
                    "error": str(e),
                    "requested_jobs": request.num_jobs,
                    "scraped_jobs": 0
                }
            }
        )

@app.options("/scrape_jobs_parallel")
async def options_scrape_jobs_parallel():
    return {"message": "OK"}

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        uvicorn.run("new_glassdoor:app", host="0.0.0.0", port=8000, reload=True)
    else:
        job_title = sys.argv[1] if len(sys.argv) > 1 else "Software Engineer"
        location = sys.argv[2] if len(sys.argv) > 2 else "San Francisco, CA"
        num_jobs = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        result = scraper_instance.scrape_jobs(job_title, location, num_jobs)
        print(json.dumps(result, indent=2))