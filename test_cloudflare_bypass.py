#!/usr/bin/env python3
"""
Simple test script to verify Cloudflare bypass functionality.
This script will test the enhanced Glassdoor scraper's ability to handle Cloudflare protection.
"""

import time
import logging
import random
from new_glassdoor import GlassdoorScraper, ScraperConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_cloudflare_bypass():
    """Test the Cloudflare bypass functionality"""
    logger.info("🚀 Starting Cloudflare bypass test...")
    
    # Create scraper with enhanced configuration
    config = ScraperConfig()
    scraper = GlassdoorScraper(config)
    
    # Create driver
    driver = scraper.driver_manager.create_driver()
    
    try:
        logger.info("📍 Step 1: Testing initial navigation to Glassdoor...")
        
        # Navigate to Glassdoor
        driver.get("https://www.glassdoor.co.in/Job/index.htm")
        
        # Wait for Cloudflare challenge
        logger.info("⏳ Waiting for Cloudflare challenge to complete...")
        cloudflare_passed = scraper.human_behavior.wait_for_cloudflare(driver, max_wait=45)
        
        if cloudflare_passed:
            logger.info("✅ Cloudflare challenge passed!")
        else:
            logger.warning("⚠️ Cloudflare challenge may still be active")
        
        # Check page content
        current_url = driver.current_url
        page_title = driver.title
        
        logger.info(f"📄 Current URL: {current_url}")
        logger.info(f"📄 Page title: {page_title}")
        
        # Check for Glassdoor content
        page_source = driver.page_source.lower()
        
        # Cloudflare indicators (bad)
        cf_indicators = [
            'checking your browser',
            'cloudflare',
            'please wait',
            'ddos protection',
            'security check',
            'ray id'
        ]
        
        # Glassdoor indicators (good)
        glassdoor_indicators = [
            'search jobs',
            'job title',
            'location',
            'glassdoor',
            'find jobs'
        ]
        
        cf_detected = any(indicator in page_source for indicator in cf_indicators)
        glassdoor_detected = any(indicator in page_source for indicator in glassdoor_indicators)
        
        if cf_detected:
            logger.error("❌ Cloudflare protection still active!")
            driver.save_screenshot("cloudflare_blocked.png")
            return False
        
        if glassdoor_detected:
            logger.info("✅ Glassdoor content detected!")
            driver.save_screenshot("glassdoor_success.png")
        else:
            logger.warning("⚠️ Glassdoor content not clearly detected")
            driver.save_screenshot("glassdoor_unclear.png")
        
        # Test search form interaction
        logger.info("📍 Step 2: Testing search form interaction...")
        
        # Add human behavior
        time.sleep(random.uniform(2, 4))
        scraper.human_behavior.simulate_human_actions(driver)
        
        # Try to find search fields
        try:
            from selenium.webdriver.common.by import By
            job_input = driver.find_element(By.ID, "searchBar-jobTitle")
            location_input = driver.find_element(By.ID, "searchBar-location")
            
            logger.info("✅ Search form fields found!")
            
            # Test filling the form (human-like)
            job_input.clear()
            time.sleep(random.uniform(0.5, 1.5))
            
            test_job = "Data Scientist"
            for char in test_job:
                job_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            time.sleep(random.uniform(1, 2))
            
            location_input.clear()
            time.sleep(random.uniform(0.5, 1.5))
            
            test_location = "Mumbai"
            for char in test_location:
                location_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            logger.info("✅ Form filled successfully!")
            driver.save_screenshot("form_filled.png")
            
            # Don't actually submit to avoid triggering more protection
            logger.info("ℹ️ Skipping form submission to avoid additional challenges")
            
        except Exception as e:
            logger.warning(f"⚠️ Search form interaction failed: {e}")
            driver.save_screenshot("form_failed.png")
        
        logger.info("🎉 Cloudflare bypass test completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        driver.save_screenshot("test_error.png")
        return False
        
    finally:
        logger.info("🔄 Keeping browser open for manual inspection...")
        input("Press Enter to close browser...")
        driver.quit()

def test_multiple_attempts():
    """Test multiple attempts to verify consistency"""
    logger.info("🔄 Testing multiple attempts for consistency...")
    
    success_count = 0
    total_attempts = 3
    
    for attempt in range(total_attempts):
        logger.info(f"🎯 Attempt {attempt + 1}/{total_attempts}")
        
        if test_cloudflare_bypass():
            success_count += 1
            logger.info(f"✅ Attempt {attempt + 1} successful")
        else:
            logger.error(f"❌ Attempt {attempt + 1} failed")
        
        if attempt < total_attempts - 1:
            logger.info("⏳ Waiting before next attempt...")
            time.sleep(10)
    
    success_rate = (success_count / total_attempts) * 100
    logger.info(f"📊 Success rate: {success_count}/{total_attempts} ({success_rate:.1f}%)")
    
    if success_rate >= 70:
        logger.info("🎉 Cloudflare bypass is working well!")
    elif success_rate >= 30:
        logger.warning("⚠️ Cloudflare bypass is partially working")
    else:
        logger.error("❌ Cloudflare bypass needs improvement")

if __name__ == "__main__":
    print("Cloudflare Bypass Test")
    print("=" * 50)
    print("1. Single test")
    print("2. Multiple attempts test")
    
    choice = input("Choose test type (1 or 2): ").strip()
    
    if choice == "2":
        test_multiple_attempts()
    else:
        test_cloudflare_bypass()
