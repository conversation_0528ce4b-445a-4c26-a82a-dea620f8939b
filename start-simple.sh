#!/bin/bash

echo "🚀 Starting Job Portal with simple setup..."

# Activate virtual environment
echo "🐍 Activating virtual environment..."
cd "$(dirname "$0")"
source .venv/bin/activate

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Please install ngrok first."
    echo "   Visit: https://ngrok.com/download"
    echo "   Or install via Homebrew: brew install ngrok/ngrok/ngrok"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $FRONTEND_PID $NGROK_PID 2>/dev/null
    exit
}

trap cleanup INT

# Start backend
echo "🔧 Starting FastAPI backend on port 8000..."
uvicorn main:app --reload --port 8000 &
BACKEND_PID=$!

# Start frontend with updated API base URL
echo "🎨 Starting Next.js frontend on port 3000..."
cd web-portal
export NEXT_PUBLIC_API_BASE_URL=""
npm run dev &
FRONTEND_PID=$!
cd ..

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 8

# Check if backend is ready
if ! curl -s http://localhost:8000/health > /dev/null; then
    echo "❌ Backend is not ready. Please check for errors."
    cleanup
fi

# Check if frontend is ready
if ! curl -s http://localhost:3000 > /dev/null; then
    echo "❌ Frontend is not ready. Please check for errors."
    cleanup
fi

echo "✅ Both services are running!"

# Create a simple nginx config for local use
cat > nginx-simple.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    server {
        listen 8080;
        server_name localhost;
        
        client_max_body_size 10M;
        
        # API and backend routes
        location ~ ^/(health|docs|redoc|openapi\.json|foundit|glassdoor|simplyhired|ziprecruiter|linkedin|naukri|scrape-linkedin|scrape-indeed)/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /health {
            proxy_pass http://127.0.0.1:8000/health;
        }
        
        # Frontend routes
        location / {
            proxy_pass http://127.0.0.1:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_cache_bypass $http_upgrade;
        }
        
        # Next.js hot reload
        location /_next/webpack-hmr {
            proxy_pass http://127.0.0.1:3000/_next/webpack-hmr;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
EOF

# Start nginx if available
if command -v nginx &> /dev/null; then
    echo "🌐 Starting nginx proxy on port 8080..."
    nginx -c "$(pwd)/nginx-simple.conf" -p "$(pwd)" &
    NGINX_PID=$!
    sleep 2
    TUNNEL_PORT=8080
    echo "✅ Nginx proxy started successfully!"
else
    echo "⚠️  nginx not available. Using direct backend access."
    TUNNEL_PORT=8000
fi

# Start ngrok tunnel
echo "🌐 Starting ngrok tunnel on port $TUNNEL_PORT..."
ngrok http $TUNNEL_PORT --log=stdout &
NGROK_PID=$!

# Wait for ngrok to start
sleep 5

# Get ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('tunnels'):
        print(data['tunnels'][0]['public_url'])
    else:
        print('No tunnels found')
except Exception as e:
    print(f'Error: {e}')
" 2>/dev/null)

if [[ $NGROK_URL == *"ngrok"* ]]; then
    echo ""
    echo "🎉 Setup complete!"
    echo ""
    echo "📱 Access your application:"
    echo "   🌐 Public URL:  $NGROK_URL"
    echo "   🏠 Local URL:   http://localhost:$TUNNEL_PORT"
    echo ""
    echo "🔗 API Endpoints (via tunnel):"
    echo "   📊 Health:      $NGROK_URL/health"
    echo "   📚 API Docs:    $NGROK_URL/docs"
    echo "   🏠 Frontend:    $NGROK_URL/"
    echo ""
    echo "🔗 Direct Local Access:"
    echo "   🎨 Frontend:    http://localhost:3000"
    echo "   🔧 Backend:     http://localhost:8000"
    echo "   📚 API Docs:    http://localhost:8000/docs"
    echo ""
    echo "📊 Monitoring:"
    echo "   🌐 ngrok Web Interface: http://localhost:4040"
    echo ""
    echo "🛑 Press Ctrl+C to stop all services"
else
    echo "❌ Failed to get ngrok URL. ngrok might still be starting..."
    echo "   Check ngrok status at: http://localhost:4040"
    echo "   Local access: http://localhost:$TUNNEL_PORT"
fi

# Keep script running
echo ""
echo "🔄 Services are running. Logs will appear below..."
echo "   Press Ctrl+C to stop everything"
echo ""

wait
