#!/usr/bin/env python3
"""
Test script to verify the enhanced Glassdoor extraction logic.
This script will test the improved parsing on the raw HTML file.
"""

import json
from bs4 import BeautifulSoup
from new_glassdoor import FieldExtractor, ScraperConfig, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_enhanced_extraction():
    """Test the enhanced extraction logic on the raw HTML"""
    print("🧪 Testing Enhanced Glassdoor Extraction")
    print("=" * 50)
    
    # Initialize components
    config = ScraperConfig()
    logger = ScraperLogger()
    extractor = FieldExtractor(config, logger)
    
    # Read the raw HTML file
    try:
        with open('raw.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        print("✅ Successfully loaded raw.html")
    except FileNotFoundError:
        print("❌ raw.html file not found")
        return
    
    # Parse with BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')
    print("✅ HTML parsed successfully")
    
    # Test the enhanced extraction
    print("\n📊 Testing Enhanced Job Description Extraction...")
    sections = extractor.extract_job_description_sections(soup)
    
    print(f"\n📋 Extracted {len(sections)} sections:")
    for section_name, content in sections.items():
        print(f"\n🔹 {section_name.upper()}:")
        if isinstance(content, list):
            print(f"   Type: List ({len(content)} items)")
            for i, item in enumerate(content[:3], 1):  # Show first 3 items
                print(f"   {i}. {item[:100]}{'...' if len(item) > 100 else ''}")
            if len(content) > 3:
                print(f"   ... and {len(content) - 3} more items")
        else:
            print(f"   Type: String")
            content_str = str(content)
            print(f"   Content: {content_str[:200]}{'...' if len(content_str) > 200 else ''}")
    
    # Test specific improvements
    print("\n🔍 Testing Specific Improvements:")
    
    # 1. Check for duplicates
    print("\n1. Duplicate Detection:")
    all_content = []
    for content in sections.values():
        if isinstance(content, list):
            all_content.extend(content)
        else:
            all_content.append(str(content))
    
    unique_content = set(all_content)
    if len(all_content) == len(unique_content):
        print("   ✅ No duplicates found")
    else:
        print(f"   ⚠️  Found {len(all_content) - len(unique_content)} duplicates")
        duplicates = [item for item in all_content if all_content.count(item) > 1]
        for dup in set(duplicates)[:3]:  # Show first 3 duplicates
            print(f"      - {dup[:100]}...")
    
    # 2. Check skills categorization
    print("\n2. Skills Categorization:")
    most_relevant = sections.get('most_relevant_skills', [])
    other_relevant = sections.get('other_relevant_skills', [])
    
    if most_relevant:
        print(f"   ✅ Most Relevant Skills: {len(most_relevant)} items")
        for skill in most_relevant[:5]:
            print(f"      • {skill}")
    else:
        print("   ⚠️  No most relevant skills found")
    
    if other_relevant:
        print(f"   ✅ Other Relevant Skills: {len(other_relevant)} items")
        for skill in other_relevant[:5]:
            print(f"      • {skill}")
    else:
        print("   ⚠️  No other relevant skills found")
    
    # 3. Check education extraction
    print("\n3. Education Extraction:")
    education = sections.get('education', None)
    if education:
        print("   ✅ Education found:")
        if isinstance(education, list):
            for edu in education:
                print(f"      • {edu}")
        else:
            print(f"      • {education}")
    else:
        print("   ⚠️  No education information found")
    
    # 4. Check location extraction
    print("\n4. Location Extraction:")
    location = sections.get('work_location', None)
    if location:
        print(f"   ✅ Work Location: {location}")
    else:
        print("   ⚠️  No work location found")
    
    # 5. Test markdown generation
    print("\n5. Markdown Generation Test:")
    try:
        # Create a mock scraper instance to access the markdown method
        from new_glassdoor import GlassdoorScraper
        scraper = GlassdoorScraper(config)
        markdown = scraper._generate_clean_markdown(sections)
        
        print("   ✅ Markdown generated successfully")
        print(f"   📄 Length: {len(markdown)} characters")
        lines_count = len(markdown.split('\n'))
        print(f"   📄 Lines: {lines_count} lines")

        # Show first few lines
        lines = markdown.split('\n')[:10]
        print("   📄 Preview:")
        for line in lines:
            print(f"      {line}")
        if len(markdown.split('\n')) > 10:
            print("      ...")
        
        # Save markdown to file for inspection
        with open('test_output.md', 'w', encoding='utf-8') as f:
            f.write(markdown)
        print("   💾 Full markdown saved to test_output.md")
        
    except Exception as e:
        print(f"   ❌ Markdown generation failed: {e}")
    
    # Performance comparison
    print("\n⚡ Performance Analysis:")
    import time
    
    # Time the extraction
    start_time = time.time()
    for _ in range(10):  # Run 10 times
        sections = extractor.extract_job_description_sections(soup)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"   ⏱️  Average extraction time: {avg_time:.4f} seconds")
    print(f"   🚀 Performance: {'Excellent' if avg_time < 0.1 else 'Good' if avg_time < 0.5 else 'Needs improvement'}")
    
    print("\n🎉 Enhanced extraction test completed!")
    
    # Summary
    print("\n📊 SUMMARY:")
    print(f"   • Sections extracted: {len(sections)}")
    print(f"   • Skills categorized: {len(most_relevant) + len(other_relevant)}")
    print(f"   • Education found: {'Yes' if education else 'No'}")
    print(f"   • Location found: {'Yes' if location else 'No'}")
    print(f"   • Duplicates: {'None' if len(all_content) == len(unique_content) else 'Found'}")
    print(f"   • Performance: {avg_time:.4f}s average")

if __name__ == "__main__":
    test_enhanced_extraction()
