#!/usr/bin/env python3
"""
Test script to demonstrate the realistic approach to URL handling
"""

import hashlib
from typing import Optional

def generate_pseudo_job_id(job_title: str, company_name: str) -> str:
    """Generate a pseudo job ID based on job data as fallback"""
    try:
        job_string = f"{job_title}-{company_name}".lower()
        hash_object = hashlib.md5(job_string.encode())
        pseudo_id = str(int(hash_object.hexdigest()[:8], 16))
        
        # Ensure it's at least 8 digits
        while len(pseudo_id) < 8:
            pseudo_id = "1" + pseudo_id
        
        return pseudo_id
    except Exception:
        return "99999999"  # Default fallback

def create_url_slug(text: str) -> str:
    """Create URL-friendly slug from text"""
    if not text:
        return ""
    
    import re
    slug = text.lower()
    slug = re.sub(r'[^\w\s-]', '', slug)
    slug = re.sub(r'[-\s]+', '-', slug)
    slug = slug.strip('-')
    
    if len(slug) > 50:
        slug = slug[:50].rstrip('-')
    
    return slug

def create_enhanced_location_slug(location: str) -> str:
    """Create enhanced location slug that matches foundit.in format"""
    if not location:
        return "india"
    
    location_mappings = {
        'pune': 'pune-maharashtra',
        'delhi': 'delhi-new-delhi',
        'mumbai': 'mumbai-maharashtra',
        'hyderabad': 'hyderabad-secunderabad-telangana',
        'bangalore': 'bengaluru-bangalore-karnataka',
        'bengaluru': 'bengaluru-bangalore-karnataka',
        'chennai': 'chennai-tamil-nadu'
    }
    
    location_clean = location.lower().strip()
    if location_clean in location_mappings:
        return location_mappings[location_clean]
    
    return create_url_slug(location)

def construct_pseudo_url(job_title: str, company_name: str, location: str, job_id: str) -> str:
    """Construct a pseudo URL for reference purposes"""
    try:
        title_slug = create_url_slug(job_title)
        company_slug = create_url_slug(company_name)
        location_slug = create_enhanced_location_slug(location)
        
        url_path = f"{title_slug}-{company_slug}-{location_slug}-{job_id}"
        
        # Generate sample search parameters
        import uuid
        search_id = str(uuid.uuid4())
        child_search_id = str(uuid.uuid4())
        search_params = f"?searchId={search_id}&child_search_id={child_search_id}"
        
        return f"https://www.foundit.in/job/{url_path}{search_params}"
        
    except Exception as e:
        return f"ERROR_CONSTRUCTING_URL: {e}"

def simulate_job_extraction():
    """Simulate the realistic job extraction process"""
    
    # Sample jobs from your output
    sample_jobs = [
        {
            "title": "Data Scientist",
            "company": "Talent Corner Hr Services Private Limited",
            "location": "Pune",
            "experience": "6-11 yrs",
            "posted_date": "a day ago",
            "real_job_id": "34347387",  # This would only be known after navigation
            "navigation_success": False  # Simulate navigation failure
        },
        {
            "title": "Data Scientist BLR",
            "company": "PHOTON",
            "location": "Delhi, Mumbai, Pune",
            "experience": "6-9 yrs",
            "posted_date": "5 days ago",
            "real_job_id": "12345678",
            "navigation_success": True  # Simulate navigation success
        }
    ]
    
    print("=== Realistic Job URL Extraction Simulation ===\n")
    
    for i, job in enumerate(sample_jobs, 1):
        print(f"Job {i}: {job['title']}")
        print(f"🏢 Company: {job['company']}")
        print(f"📍 Location: {job['location']}")
        print(f"💼 Experience: {job['experience']}")
        print(f"📅 Posted: {job['posted_date']}")
        
        # Simulate the extraction process
        print("\n--- URL Extraction Process ---")
        
        # Step 1: Check for direct links in search results
        print("Step 1: Checking for direct links in search results...")
        print("❌ No direct job URLs found in search result HTML")
        
        # Step 2: Mark for navigation
        print("Step 2: Marking job for click-through navigation...")
        job_url_status = "NEEDS_NAVIGATION"
        print(f"✅ Status: {job_url_status}")
        
        # Step 3: Simulate click-through navigation
        print("Step 3: Attempting click-through navigation...")
        if job['navigation_success']:
            # Navigation successful - we get the real URL
            real_url = f"https://www.foundit.in/job/data-scientist-{job['real_job_id']}"
            print(f"✅ Navigation successful!")
            print(f"🔗 Real URL: {real_url}")
            final_url = real_url
            url_type = "NAVIGATED"
        else:
            # Navigation failed - use pseudo URL
            print("❌ Navigation failed (common scenario)")
            print("Step 4: Generating pseudo URL as fallback...")
            
            pseudo_id = generate_pseudo_job_id(job['title'], job['company'])
            pseudo_url = construct_pseudo_url(
                job['title'], 
                job['company'], 
                job['location'].split(',')[0].strip(),  # Use first location
                pseudo_id
            )
            
            print(f"🔧 Generated pseudo ID: {pseudo_id}")
            print(f"🔗 Pseudo URL: {pseudo_url}")
            final_url = pseudo_url
            url_type = "CONSTRUCTED"
        
        print(f"\n--- Final Result ---")
        print(f"📊 URL Type: {url_type}")
        print(f"🔗 Final URL: {final_url}")
        
        # Show what information we DO have reliably
        print(f"\n--- Reliable Information Extracted ---")
        print(f"✅ Job Title: {job['title']}")
        print(f"✅ Company: {job['company']}")
        print(f"✅ Location: {job['location']}")
        print(f"✅ Experience: {job['experience']}")
        print(f"✅ Posted Date: {job['posted_date']}")
        
        print("=" * 80)
    
    print("\n=== Summary ===")
    print("✅ What works reliably:")
    print("   - Basic job information extraction from search results")
    print("   - Pseudo URL generation for reference")
    print("   - Graceful handling of navigation failures")
    
    print("\n❌ What doesn't work consistently:")
    print("   - Direct URL construction from search results (job IDs not available)")
    print("   - 100% success rate for click-through navigation")
    print("   - Guaranteed working URLs without actual page navigation")
    
    print("\n💡 Recommendation:")
    print("   - Use the hybrid approach: extract what's available, navigate when needed")
    print("   - Accept that some jobs may only have pseudo URLs")
    print("   - Focus on the rich information that IS reliably available")

if __name__ == "__main__":
    simulate_job_extraction()
