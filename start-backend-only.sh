#!/bin/bash

echo "🚀 Starting Job Portal Backend with ngrok..."

# Activate virtual environment
echo "🐍 Activating virtual environment..."
cd "$(dirname "$0")"
source .venv/bin/activate

# Check if ngrok is available
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not found in PATH. Checking venv..."
    if [ -f "venv/bin/ngrok" ]; then
        export PATH="$(pwd)/venv/bin:$PATH"
        echo "✅ Found ngrok in venv"
    else
        echo "❌ ngrok is not installed. Please install ngrok first."
        echo "   Visit: https://ngrok.com/download"
        exit 1
    fi
fi

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $NGROK_PID 2>/dev/null
    exit
}

trap cleanup INT

# Start backend
echo "🔧 Starting FastAPI backend on port 8000..."
uvicorn main:app --reload --port 8000 &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 8

# Check if backend is ready
echo "🔍 Checking backend health..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "✅ Backend is ready!"
        break
    else
        echo "   Attempt $i/10: Backend not ready yet..."
        sleep 2
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ Backend failed to start. Please check for errors."
        echo "   Try running manually: uvicorn main:app --reload --port 8000"
        cleanup
    fi
done

# Start ngrok tunnel
echo "🌐 Starting ngrok tunnel on port 8000..."
ngrok http 8000 --log=stdout &
NGROK_PID=$!

# Wait for ngrok to start
sleep 5

# Get ngrok URL
echo "🔍 Getting ngrok URL..."
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('tunnels') and len(data['tunnels']) > 0:
        print(data['tunnels'][0]['public_url'])
    else:
        print('No tunnels found')
except Exception as e:
    print(f'Error: {e}')
" 2>/dev/null)

if [[ $NGROK_URL == *"ngrok"* ]]; then
    echo ""
    echo "🎉 Backend setup complete!"
    echo ""
    echo "📱 Access your backend API:"
    echo "   🌐 Public URL:     $NGROK_URL"
    echo "   🏠 Local URL:      http://localhost:8000"
    echo ""
    echo "🔗 API Endpoints (via tunnel):"
    echo "   📊 Health:         $NGROK_URL/health"
    echo "   📚 API Docs:       $NGROK_URL/docs"
    echo "   🔍 Interactive:    $NGROK_URL/redoc"
    echo ""
    echo "🔗 Scraper Endpoints:"
    echo "   🔍 Foundit:        $NGROK_URL/foundit/scrape_foundit"
    echo "   🔍 Glassdoor:      $NGROK_URL/glassdoor/scrape_jobs_parallel"
    echo "   🔍 SimplyHired:    $NGROK_URL/simplyhired/scrape_simplyhired"
    echo "   🔍 ZipRecruiter:   $NGROK_URL/ziprecruiter/scrape_ziprecruiter"
    echo "   🔍 LinkedIn:       $NGROK_URL/linkedin/scrape-linkedin/"
    echo "   🔍 Indeed:         $NGROK_URL/scrape-indeed/"
    echo "   🔍 Naukri:         $NGROK_URL/naukri/scrape-naukri/"
    echo ""
    echo "📊 Monitoring:"
    echo "   🌐 ngrok Web Interface: http://localhost:4040"
    echo ""
    echo "💡 To test an endpoint, try:"
    echo "   curl -X POST $NGROK_URL/health"
    echo ""
    echo "🛑 Press Ctrl+C to stop all services"
else
    echo "❌ Failed to get ngrok URL. ngrok might still be starting..."
    echo "   Check ngrok status at: http://localhost:4040"
    echo "   Backend is running at: http://localhost:8000"
    echo "   API docs: http://localhost:8000/docs"
fi

# Keep script running
echo ""
echo "🔄 Backend is running. Logs will appear below..."
echo "   Press Ctrl+C to stop everything"
echo ""

wait
