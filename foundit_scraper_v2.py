import time
import logging
import random
import uuid
from typing import Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from urllib.parse import quote_plus, urljoin
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, ElementClickInterceptedException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
import json
from fastapi import FastAPI, HTTPException, Body
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import undetected_chromedriver as uc
from pydantic import BaseModel
import threading
import re
from urllib.parse import urlparse, parse_qs
from selenium.webdriver.chrome.options import Options

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper v2", version="2.0")

# CORS configuration
allowed_origins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://localhost:3001", 
    "https://localhost:3001",
    "*"  # Allow all origins for development
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@dataclass
class JobData:
    """Data structure for job information based on job-page.md structure"""
    job_id: str = ""
    title: str = ""
    company_name: str = ""
    location: str = ""
    experience: str = ""
    salary: str = ""
    job_description: str = ""
    skills: List[str] = None  # type: ignore
    posted_date: str = ""
    job_url: str = ""
    company_description: str = ""
    role: str = ""
    industry: str = ""
    function: str = ""
    job_type: str = ""
    applicants_count: str = ""
    
    def __post_init__(self):
        if self.skills is None:
            self.skills = []

class JobSearchRequest(BaseModel):
    """Request model for job search"""
    job_title: str
    location: str
    num_jobs: int = 5


class NetworkInterceptor:
    """Class to intercept and capture network requests for job data"""

    def __init__(self):
        self.captured_requests = []
        self.job_data_responses = []
        self.api_endpoints = []

    def capture_request(self, request_data: dict):
        """Capture network request data"""
        try:
            url = request_data.get('url', '')
            method = request_data.get('method', '')

            # Look for job-related API endpoints
            if any(keyword in url.lower() for keyword in ['job', 'search', 'api', 'data']):
                logger.info(f"Captured API request: {method} {url}")
                self.captured_requests.append(request_data)
                self.api_endpoints.append(url)

        except Exception as e:
            logger.debug(f"Error capturing request: {e}")

    def capture_response(self, response_data: dict):
        """Capture network response data"""
        try:
            url = response_data.get('url', '')
            status = response_data.get('status', 0)

            # Look for successful responses with job data
            if status == 200 and any(keyword in url.lower() for keyword in ['job', 'search', 'api']):
                logger.info(f"Captured API response: {status} {url}")
                self.job_data_responses.append(response_data)

        except Exception as e:
            logger.debug(f"Error capturing response: {e}")

    def extract_job_data_from_responses(self) -> List[dict]:
        """Extract job data from captured responses"""
        job_data = []

        for response in self.job_data_responses:
            try:
                # Try to parse response body as JSON
                body = response.get('body', '')
                if body:
                    try:
                        data = json.loads(body)

                        # Look for job data patterns
                        if isinstance(data, dict):
                            # Check for common job data structures
                            if 'jobs' in data:
                                job_data.extend(data['jobs'])
                            elif 'data' in data and isinstance(data['data'], list):
                                job_data.extend(data['data'])
                            elif 'results' in data:
                                job_data.extend(data['results'])
                            elif 'jobData' in data:
                                job_data.append(data['jobData'])
                            elif 'jobDataArray' in data:
                                job_data.extend(data['jobDataArray'])

                    except json.JSONDecodeError:
                        # Response might not be JSON
                        continue

            except Exception as e:
                logger.debug(f"Error extracting job data from response: {e}")

        logger.info(f"Extracted {len(job_data)} job records from network responses")
        return job_data


class FounditScraperV2:
    """Enhanced Foundit scraper following the specified workflow"""
    
    def __init__(self, headless: bool = True, timeout: int = 30):
        self.headless = headless
        self.timeout = timeout
        self.driver: Optional[uc.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        self.base_url = "https://www.foundit.in/"
        self.network_interceptor = NetworkInterceptor()
        
        # Selectors based on the specified workflow
        self.selectors = {
            # Cookie consent
            'cookie_banner': '#cookieBanner',
            'accept_cookies': '#acceptAll',

            # Search form
            'search_form': '#searchForm',
            'job_title_input': '#heroSectionDesktop-skillsAutoComplete--input',
            'location_input': '#heroSectionDesktop-locationAutoComplete--input',
            'search_button': 'button[type="submit"]',

            # Job cards on results page
            'job_cards': 'div[data-index]',

            # Job detail page elements (simplified selectors)
            'job_detail_title': 'h1',
            'job_detail_company': 'a[href*="jobs-career"]',
            'job_detail_experience': '.flex.items-center.gap-2 span',
            'job_detail_location': 'a[href*="jobs-in-"]',
            'job_detail_industry': '.text-content-primary',
            'job_detail_description': '#jobDescription .break-words',
            'job_detail_skills': '#skillSectionNew a, #skillSectionNew label',
            'job_detail_posted_date': '.text-content-tertiary',
            'job_detail_job_id': '.text-content-tertiary',
            'job_detail_role': 'a',
            'job_detail_function': 'a',
            'job_detail_job_type': 'a',
            'job_detail_applicants': '.text-content-tertiary',
            'company_description': '#jobCompany p'
        }

    def setup_driver(self) -> uc.Chrome:
        """Setup Chrome driver with anti-detection measures and network monitoring"""
        try:
            options = uc.ChromeOptions()

            # Basic options
            if self.headless:
                options.add_argument('--headless=new')
                options.add_argument('--window-size=1920,1080')

            # Anti-detection options
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')

            # Performance optimizations (but keep some logging for network monitoring)
            options.add_argument('--disable-images')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-popup-blocking')

            # Enable performance logging for network monitoring
            options.add_argument('--enable-logging')
            options.add_argument('--log-level=0')

            # Random user agent
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            options.add_argument(f'--user-agent={random.choice(user_agents)}')

            # Enable performance logging capabilities
            caps = DesiredCapabilities.CHROME.copy()
            caps['goog:loggingPrefs'] = {'performance': 'ALL'}  # type: ignore

            self.driver = uc.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, self.timeout)

            logger.info("Chrome driver initialized successfully with network monitoring")
            return self.driver

        except Exception as e:
            logger.error(f"Failed to setup driver: {e}")
            raise WebDriverException(f"Driver setup failed: {e}")

    def enable_network_logging(self):
        """Enable network request/response logging using Chrome DevTools Protocol"""
        try:
            if not self.driver:
                return

            # Enable network domain
            self.driver.execute_cdp_cmd('Network.enable', {})

            # Enable runtime domain for console logs
            self.driver.execute_cdp_cmd('Runtime.enable', {})

            logger.info("Network logging enabled successfully")

        except Exception as e:
            logger.warning(f"Failed to enable network logging: {e}")

    def capture_network_logs(self) -> List[dict]:
        """Capture network logs using Chrome DevTools Protocol directly"""
        try:
            if not self.driver:
                return []

            # Alternative approach: Use CDP to get network events
            network_logs = []

            try:
                # Get all network events from CDP
                network_events = self.driver.execute_cdp_cmd('Network.getAllCookies', {})
                logger.debug(f"Retrieved network events: {len(network_events) if network_events else 0}")

                # Try to get page resources
                page_resources = self.driver.execute_cdp_cmd('Page.getResourceTree', {})
                if page_resources and 'frameTree' in page_resources:
                    frame_tree = page_resources['frameTree']
                    resources = frame_tree.get('resources', [])

                    for resource in resources:
                        url = resource.get('url', '')
                        if any(keyword in url.lower() for keyword in ['job', 'search', 'api', 'data']):
                            request_data = {
                                'url': url,
                                'method': 'GET',
                                'type': resource.get('type', ''),
                                'timestamp': time.time()
                            }
                            self.network_interceptor.capture_request(request_data)
                            network_logs.append(request_data)

            except Exception as cdp_error:
                logger.debug(f"CDP network capture failed: {cdp_error}")

            # Alternative: Try to intercept XHR/Fetch requests using JavaScript
            try:
                xhr_data = self.driver.execute_script("""
                    // Check if we have any stored network requests
                    if (window.capturedRequests) {
                        return window.capturedRequests;
                    }

                    // Look for any global variables that might contain job data
                    var possibleData = [];

                    // Check common global variables
                    var globalVars = ['jobsData', 'searchResults', 'jobResults', 'jobs', '__NEXT_DATA__'];
                    for (var i = 0; i < globalVars.length; i++) {
                        if (window[globalVars[i]]) {
                            possibleData.push({
                                source: globalVars[i],
                                data: window[globalVars[i]]
                            });
                        }
                    }

                    return possibleData;
                """)

                if xhr_data:
                    logger.info(f"Found {len(xhr_data)} potential data sources from JavaScript")
                    for data_source in xhr_data:
                        # Process the data source
                        source_name = data_source.get('source', '')
                        source_data = data_source.get('data', {})

                        # Try to extract job data from the source
                        if isinstance(source_data, dict):
                            self.network_interceptor.job_data_responses.append({
                                'url': f'javascript:{source_name}',
                                'status': 200,
                                'body': json.dumps(source_data)
                            })

            except Exception as js_error:
                logger.debug(f"JavaScript data extraction failed: {js_error}")

            # Try to capture any fetch/XHR requests that might be in progress
            try:
                # Inject network monitoring script
                monitoring_script = """
                    // Override fetch to capture requests
                    if (!window.originalFetch) {
                        window.originalFetch = window.fetch;
                        window.capturedRequests = [];

                        window.fetch = function(...args) {
                            var url = args[0];
                            if (typeof url === 'string' && (url.includes('job') || url.includes('search') || url.includes('api'))) {
                                window.capturedRequests.push({
                                    url: url,
                                    method: 'GET',
                                    timestamp: Date.now()
                                });
                            }
                            return window.originalFetch.apply(this, args);
                        };
                    }

                    return window.capturedRequests || [];
                """

                captured_requests = self.driver.execute_script(monitoring_script)
                if captured_requests:
                    logger.info(f"Captured {len(captured_requests)} fetch requests")
                    for req in captured_requests:
                        self.network_interceptor.capture_request(req)
                        network_logs.append(req)

            except Exception as monitor_error:
                logger.debug(f"Network monitoring injection failed: {monitor_error}")

            logger.info(f"Captured {len(network_logs)} network events using alternative methods")
            return network_logs

        except Exception as e:
            logger.warning(f"Failed to capture network logs: {e}")
            return []

    def inject_network_monitoring(self):
        """Inject network monitoring script early in the page lifecycle"""
        try:
            if not self.driver:
                return

            monitoring_script = """
                // Create a global object to store captured requests
                window.foundItNetworkCapture = {
                    requests: [],
                    responses: [],
                    originalFetch: window.fetch,
                    originalXHR: window.XMLHttpRequest
                };

                // Override fetch
                window.fetch = function(...args) {
                    var url = args[0];
                    var options = args[1] || {};

                    if (typeof url === 'string') {
                        window.foundItNetworkCapture.requests.push({
                            url: url,
                            method: options.method || 'GET',
                            timestamp: Date.now(),
                            type: 'fetch'
                        });
                    }

                    return window.foundItNetworkCapture.originalFetch.apply(this, args)
                        .then(response => {
                            if (typeof url === 'string' && response.ok) {
                                window.foundItNetworkCapture.responses.push({
                                    url: url,
                                    status: response.status,
                                    timestamp: Date.now(),
                                    type: 'fetch'
                                });
                            }
                            return response;
                        });
                };

                // Override XMLHttpRequest
                var OriginalXHR = window.XMLHttpRequest;
                window.XMLHttpRequest = function() {
                    var xhr = new OriginalXHR();
                    var originalOpen = xhr.open;
                    var originalSend = xhr.send;

                    xhr.open = function(method, url, ...args) {
                        this._method = method;
                        this._url = url;

                        window.foundItNetworkCapture.requests.push({
                            url: url,
                            method: method,
                            timestamp: Date.now(),
                            type: 'xhr'
                        });

                        return originalOpen.apply(this, [method, url, ...args]);
                    };

                    xhr.send = function(...args) {
                        var self = this;

                        this.addEventListener('load', function() {
                            if (self.status >= 200 && self.status < 300) {
                                window.foundItNetworkCapture.responses.push({
                                    url: self._url,
                                    status: self.status,
                                    timestamp: Date.now(),
                                    type: 'xhr',
                                    responseText: self.responseText
                                });
                            }
                        });

                        return originalSend.apply(this, args);
                    };

                    return xhr;
                };

                console.log('Foundit network monitoring injected successfully');
            """

            self.driver.execute_script(monitoring_script)
            logger.info("Network monitoring script injected successfully")

        except Exception as e:
            logger.warning(f"Failed to inject network monitoring: {e}")

    def get_captured_network_data(self) -> List[dict]:
        """Retrieve captured network data from injected monitoring script"""
        try:
            if not self.driver:
                return []

            captured_data = self.driver.execute_script("""
                if (window.foundItNetworkCapture) {
                    return {
                        requests: window.foundItNetworkCapture.requests,
                        responses: window.foundItNetworkCapture.responses
                    };
                }
                return null;
            """)

            if captured_data:
                requests = captured_data.get('requests', [])
                responses = captured_data.get('responses', [])

                logger.info(f"Retrieved {len(requests)} requests and {len(responses)} responses from network monitoring")

                # Process responses to extract job data
                for response in responses:
                    if response.get('responseText'):
                        try:
                            response_json = json.loads(response['responseText'])
                            self.network_interceptor.job_data_responses.append({
                                'url': response.get('url', ''),
                                'status': response.get('status', 200),
                                'body': response['responseText']
                            })
                        except json.JSONDecodeError:
                            continue

                return requests + responses

            return []

        except Exception as e:
            logger.warning(f"Failed to get captured network data: {e}")
            return []

    def convert_network_job_data(self, job_data: dict) -> JobData:
        """Convert network job data to our JobData format"""
        converted_job = JobData()

        try:
            # Extract job ID
            converted_job.job_id = str(job_data.get('id', '') or job_data.get('jobId', ''))

            # Extract job title
            converted_job.title = job_data.get('title', '') or job_data.get('jobTitle', '')

            # Extract company name
            converted_job.company_name = job_data.get('companyName', '') or job_data.get('company', '')

            # Extract location
            locations = job_data.get('locations', [])
            if locations and isinstance(locations, list):
                converted_job.location = ', '.join([loc.get('name', '') for loc in locations if isinstance(loc, dict)])
            else:
                converted_job.location = job_data.get('location', '') or job_data.get('city', '')

            # Extract experience
            min_exp = job_data.get('minimumExperience', {})
            max_exp = job_data.get('maximumExperience', {})
            if isinstance(min_exp, dict) and isinstance(max_exp, dict):
                min_years = min_exp.get('years', 0)
                max_years = max_exp.get('years', 0)
                if min_years or max_years:
                    converted_job.experience = f"{min_years}-{max_years} years"
            else:
                converted_job.experience = job_data.get('experience', '')

            # Extract salary
            converted_job.salary = job_data.get('salary', '') or job_data.get('salaryRange', '')

            # Extract job description
            converted_job.job_description = job_data.get('description', '') or job_data.get('jobDescription', '')

            # Extract skills
            skills = job_data.get('itSkills', []) or job_data.get('skills', [])
            if isinstance(skills, list):
                converted_job.skills = [skill.get('text', '') if isinstance(skill, dict) else str(skill) for skill in skills]

            # Extract posted date
            converted_job.posted_date = job_data.get('postedDate', '') or job_data.get('createdDate', '')

            # Construct job URL if we have job ID
            if converted_job.job_id and converted_job.title and converted_job.company_name:
                constructed_url = self.construct_job_url_from_data({
                    'jobId': converted_job.job_id,
                    'title': converted_job.title,
                    'company': converted_job.company_name,
                    'location': converted_job.location
                })
                if constructed_url:
                    converted_job.job_url = constructed_url

            # Extract additional fields
            converted_job.company_description = job_data.get('companyDescription', '')
            converted_job.role = job_data.get('role', '') or job_data.get('jobRole', '')
            converted_job.industry = job_data.get('industry', '')
            converted_job.function = job_data.get('function', '') or job_data.get('jobFunction', '')
            converted_job.job_type = job_data.get('jobType', '') or job_data.get('employmentType', '')
            converted_job.applicants_count = str(job_data.get('applicantsCount', '') or job_data.get('applicationCount', ''))

            logger.info(f"Converted network job data - Title: {converted_job.title}, Company: {converted_job.company_name}, ID: {converted_job.job_id}")
            return converted_job

        except Exception as e:
            logger.error(f"Error converting network job data: {e}")
            return converted_job

    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to mimic human behavior"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    def get_job_urls_from_network(self) -> List[str]:
        """Monitor network traffic to capture job detail URLs"""
        if not self.driver:
            return []

        try:
            logs = self.driver.get_log('performance')
            job_urls = []

            for log in logs:
                try:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.responseReceived':
                        url = message['message']['params']['response']['url']
                        # Look for job detail URLs
                        if ('job' in url.lower() and
                            'foundit.in' in url and
                            'search' not in url and
                            url not in job_urls):
                            job_urls.append(url)
                except:
                    continue

            return job_urls

        except Exception as e:
            logger.debug(f"Error getting job URLs from network: {e}")
            return []

    def advanced_click_job_card(self, card_element) -> Optional[str]:
        """Advanced click strategy with network monitoring to get actual job URL"""
        if not self.driver:
            return None

        try:
            # Get initial network state
            initial_urls = self.get_job_urls_from_network()
            current_url = self.driver.current_url

            logger.info("Attempting advanced click with complete mouse simulation")

            # Strategy 1: Complete JavaScript mouse event simulation
            success = self.driver.execute_script("""
                var element = arguments[0];
                var rect = element.getBoundingClientRect();
                var centerX = rect.left + rect.width / 2;
                var centerY = rect.top + rect.height / 2;

                // Simulate complete mouse interaction lifecycle
                var events = [
                    'mouseover', 'mouseenter', 'mousemove',
                    'mousedown', 'focus', 'mouseup', 'click'
                ];

                var success = false;

                events.forEach(function(eventType, index) {
                    setTimeout(function() {
                        try {
                            var event = new MouseEvent(eventType, {
                                view: window,
                                bubbles: true,
                                cancelable: true,
                                clientX: centerX,
                                clientY: centerY,
                                screenX: centerX + window.screenX,
                                screenY: centerY + window.screenY,
                                button: 0,
                                buttons: eventType === 'mousedown' ? 1 : 0,
                                detail: eventType === 'click' ? 1 : 0
                            });

                            element.dispatchEvent(event);

                            // Also trigger on parent elements (event bubbling)
                            var parent = element.parentElement;
                            while(parent && parent !== document) {
                                parent.dispatchEvent(event);
                                parent = parent.parentElement;
                            }

                            if (eventType === 'click') {
                                success = true;
                            }
                        } catch(e) {
                            console.log('Event dispatch error:', e);
                        }
                    }, index * 50); // Stagger events
                });

                return true;
            """, card_element)

            # Wait for potential navigation or AJAX calls
            self.random_delay(3, 5)

            # Check for URL change
            new_url = self.driver.current_url
            if new_url != current_url:
                logger.info(f"Navigation detected: {new_url}")
                return new_url

            # Check for new job URLs in network traffic
            new_urls = self.get_job_urls_from_network()
            job_urls = list(set(new_urls) - set(initial_urls))

            logger.info(f"Initial URLs: {len(initial_urls)}, New URLs: {len(new_urls)}, Job URLs found: {len(job_urls)}")
            if new_urls:
                logger.info(f"Sample new URLs: {new_urls[:3]}")

            if job_urls:
                logger.info(f"Job URL detected from network: {job_urls[0]}")
                return job_urls[0]

            # Strategy 2: Try to find and click specific clickable elements
            clickable_selectors = [
                'a[href*="job"]',
                'h3 a',
                '.job-title a',
                'a',
                '[role="button"]',
                '.cursor-pointer'
            ]

            for selector in clickable_selectors:
                try:
                    clickable_elements = card_element.find_elements(By.CSS_SELECTOR, selector)
                    if clickable_elements:
                        element = clickable_elements[0]
                        href = element.get_attribute('href')

                        if href and 'job' in href and 'foundit.in' in href:
                            logger.info(f"Found direct job URL: {href}")
                            return href

                        # Try clicking the element
                        try:
                            element.click()
                            self.random_delay(2, 3)

                            new_url = self.driver.current_url
                            if new_url != current_url:
                                logger.info(f"Navigation via {selector}: {new_url}")
                                return new_url
                        except:
                            continue

                except:
                    continue

            logger.warning("Advanced click strategies failed to find job URL")
            return None

        except Exception as e:
            logger.error(f"Error in advanced click: {e}")
            return None

    def navigate_to_homepage(self) -> bool:
        """Navigate to Foundit homepage"""
        try:
            if not self.driver or not self.wait:
                logger.error("Driver or wait not initialized")
                return False

            logger.info(f"Navigating to {self.base_url}")
            self.driver.get(self.base_url)
            self.random_delay(2, 4)

            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            logger.info("Successfully navigated to Foundit homepage")
            return True

        except Exception as e:
            logger.error(f"Failed to navigate to homepage: {e}")
            return False

    def handle_cookie_consent(self) -> bool:
        """Handle cookie consent banner by clicking 'Okay' button"""
        try:
            if not self.wait:
                logger.error("Wait not initialized")
                return False

            logger.info("Checking for cookie consent banner")

            # Wait for cookie banner to appear
            cookie_banner = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['cookie_banner']))
            )

            # Find and click the accept button
            accept_button = cookie_banner.find_element(By.CSS_SELECTOR, self.selectors['accept_cookies'])
            accept_button.click()

            logger.info("Cookie consent accepted")
            self.random_delay(1, 2)
            return True

        except TimeoutException:
            logger.info("No cookie consent banner found")
            return True
        except Exception as e:
            logger.warning(f"Failed to handle cookie consent: {e}")
            return True  # Continue even if cookie handling fails

    def fill_search_form(self, job_title: str, location: str) -> bool:
        """Fill in the search form with job title and location"""
        try:
            if not self.wait:
                logger.error("Wait not initialized")
                return False

            logger.info(f"Filling search form with job_title='{job_title}', location='{location}'")

            # Wait for search form to be present
            search_form = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['search_form']))
            )
            
            # Fill job title/skills input
            job_title_input = search_form.find_element(By.CSS_SELECTOR, self.selectors['job_title_input'])
            job_title_input.clear()
            job_title_input.send_keys(job_title)
            self.random_delay(0.5, 1)
            
            # Fill location input
            location_input = search_form.find_element(By.CSS_SELECTOR, self.selectors['location_input'])
            location_input.clear()
            location_input.send_keys(location)
            self.random_delay(0.5, 1)
            
            # Click search button
            search_button = search_form.find_element(By.CSS_SELECTOR, self.selectors['search_button'])
            search_button.click()
            
            logger.info("Search form submitted successfully")
            self.random_delay(3, 5)  # Wait for results to load
            return True
            
        except Exception as e:
            logger.error(f"Failed to fill search form: {e}")
            return False

    def wait_for_search_results(self) -> bool:
        """Wait for search results page to load with extended timeout for dynamic content"""
        try:
            if not self.wait:
                logger.error("Wait not initialized")
                return False

            # Wait for job cards to appear
            self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
            )
            logger.info("Search results page loaded successfully")

            # Additional wait for dynamic content to load
            logger.info("Waiting for dynamic job content to load...")
            self.random_delay(10, 15)  # Wait longer for JavaScript to execute

            # Try to wait for actual job content to appear
            try:
                # Look for job titles or company names to appear
                if self.driver:
                    WebDriverWait(self.driver, 20).until(
                        lambda driver: len(driver.find_elements(By.CSS_SELECTOR, 'h1, h2, h3, .job-title, [data-testid="job-title"]')) > 0
                    )
                    logger.info("Dynamic job content loaded successfully")
            except TimeoutException:
                logger.warning("Timeout waiting for dynamic job content, proceeding anyway")

            # Additional wait to ensure all content is loaded
            self.random_delay(5, 8)

            return True

        except TimeoutException:
            logger.error("Search results page did not load within timeout")
            return False
        except Exception as e:
            logger.error(f"Error waiting for search results: {e}")
            return False

    def get_job_cards(self) -> List:
        """Get all job cards from the search results page"""
        try:
            if not self.driver:
                logger.error("Driver not initialized")
                return []

            job_cards = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['job_cards'])
            logger.info(f"Found {len(job_cards)} job cards on the page")
            return job_cards

        except Exception as e:
            logger.error(f"Failed to get job cards: {e}")
            return []

    def click_job_card(self, card_element) -> bool:
        """Click on a job card to navigate to job detail page"""
        try:
            if not self.driver:
                logger.error("Driver not initialized")
                return False

            # Store current URL to detect navigation
            current_url = self.driver.current_url
            logger.info(f"Current URL before click: {current_url}")

            # Try to find clickable elements within the card
            clickable_selectors = [
                'h3',  # Job title
                'a',   # Any link
                '.cursor-pointer',  # Elements with cursor pointer
                '[role="button"]',  # Button elements
                'div'  # The card div itself
            ]

            # First try clicking on specific elements within the card
            for selector in clickable_selectors:
                try:
                    clickable_elements = card_element.find_elements(By.CSS_SELECTOR, selector)
                    if clickable_elements:
                        element = clickable_elements[0]
                        logger.info(f"Trying to click on {selector} element: {element.tag_name}")

                        # Try multiple click methods on this element
                        for method_name in ['direct', 'action_chains', 'javascript']:
                            try:
                                if method_name == 'direct':
                                    element.click()
                                elif method_name == 'action_chains':
                                    ActionChains(self.driver).click(element).perform()
                                elif method_name == 'javascript':
                                    self.driver.execute_script("arguments[0].click();", element)

                                self.random_delay(3, 5)

                                # Check if URL changed or if modal/content appeared
                                new_url = self.driver.current_url
                                logger.info(f"URL after click attempt: {new_url}")

                                if new_url != current_url:
                                    logger.info(f"Successfully navigated using {selector} with {method_name} method")
                                    return True

                                # Check if a modal or job detail content appeared
                                modal_selectors = [
                                    '.modal', '.popup', '.overlay',
                                    '#jobDetailContainer', '.job-detail',
                                    '[role="dialog"]', '.dialog'
                                ]

                                for modal_selector in modal_selectors:
                                    try:
                                        modal_elements = self.driver.find_elements(By.CSS_SELECTOR, modal_selector)
                                        if modal_elements and modal_elements[0].is_displayed():
                                            logger.info(f"Modal/popup detected with selector: {modal_selector}")
                                            return True
                                    except:
                                        continue

                            except Exception as e:
                                logger.debug(f"Click method {method_name} on {selector} failed: {e}")
                                continue

                except Exception as e:
                    logger.debug(f"Failed to find or click {selector}: {e}")
                    continue

            # If specific elements didn't work, try clicking the card itself
            logger.info("Trying to click the card element directly")
            for i, strategy_name in enumerate(['direct_click', 'action_chains', 'javascript']):
                try:
                    if strategy_name == 'direct_click':
                        card_element.click()
                    elif strategy_name == 'action_chains':
                        ActionChains(self.driver).click(card_element).perform()
                    elif strategy_name == 'javascript':
                        self.driver.execute_script("arguments[0].click();", card_element)

                    self.random_delay(3, 5)

                    # Check if URL changed (indicating successful navigation)
                    new_url = self.driver.current_url
                    logger.info(f"URL after card click: {new_url}")

                    if new_url != current_url:
                        logger.info(f"Successfully navigated to job detail page using strategy {i+1}")
                        return True

                except Exception as e:
                    logger.debug(f"Click strategy {i+1} ({strategy_name}) failed: {e}")
                    continue

            logger.warning("All click strategies failed")
            return False

        except Exception as e:
            logger.error(f"Failed to click job card: {e}")
            return False

    def extract_job_detail_data(self) -> JobData:
        """Extract detailed job information from job detail page"""
        job_data = JobData()

        if not self.driver:
            logger.error("Driver not initialized")
            return job_data

        try:
            # Extract job title
            try:
                title_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_title'])
                job_data.title = title_element.text.strip()
            except NoSuchElementException:
                # Fallback selectors for title
                fallback_title_selectors = ['h1', '.job-title', '[data-testid="job-title"]']
                for selector in fallback_title_selectors:
                    try:
                        title_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        job_data.title = title_element.text.strip()
                        break
                    except NoSuchElementException:
                        continue

            # Extract company name
            try:
                company_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_company'])
                job_data.company_name = company_element.text.strip()
            except NoSuchElementException:
                # Fallback selectors for company
                fallback_company_selectors = ['.company-name', '[data-testid="company-name"]', 'a[href*="jobs-career"]']
                for selector in fallback_company_selectors:
                    try:
                        company_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        job_data.company_name = company_element.text.strip()
                        break
                    except NoSuchElementException:
                        continue

            # Extract experience
            try:
                experience_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_experience'])
                job_data.experience = experience_element.text.strip()
            except NoSuchElementException:
                # Look for experience in text content
                try:
                    page_text = self.driver.page_source
                    import re
                    exp_match = re.search(r'(\d+[-\s]*\d*\s*(?:Years?|yrs?))', page_text, re.IGNORECASE)
                    if exp_match:
                        job_data.experience = exp_match.group(1)
                except:
                    pass

            # Extract location
            try:
                location_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_location'])
                job_data.location = location_element.text.strip()
            except NoSuchElementException:
                # Fallback selectors for location
                fallback_location_selectors = ['.location', '[data-testid="location"]', 'a[href*="jobs-in-"]']
                for selector in fallback_location_selectors:
                    try:
                        location_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        job_data.location = location_element.text.strip()
                        break
                    except NoSuchElementException:
                        continue

            # Extract job description
            try:
                desc_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_description'])
                job_data.job_description = desc_element.text.strip()
            except NoSuchElementException:
                # Fallback selectors for description
                fallback_desc_selectors = [
                    '#jobDescription',
                    '.job-description-content',
                    '.job-description',
                    '.description',
                    '[data-testid="job-description"]'
                ]
                for selector in fallback_desc_selectors:
                    try:
                        desc_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        job_data.job_description = desc_element.text.strip()
                        break
                    except NoSuchElementException:
                        continue

            # Extract skills
            try:
                skill_elements = self.driver.find_elements(By.CSS_SELECTOR, self.selectors['job_detail_skills'])
                job_data.skills = [skill.text.strip() for skill in skill_elements if skill.text.strip()]
            except NoSuchElementException:
                job_data.skills = []

            # Extract additional details
            self._extract_additional_details(job_data)

            # Set job URL
            job_data.job_url = self.driver.current_url

            logger.info(f"Successfully extracted job data for: {job_data.title}")
            return job_data

        except Exception as e:
            logger.error(f"Error extracting job detail data: {e}")
            return job_data

    def _extract_additional_details(self, job_data: JobData):
        """Extract additional job details like posted date, job ID, etc."""
        if not self.driver:
            return

        try:
            # Extract posted date
            try:
                posted_elements = self.driver.find_elements(By.XPATH, "//p[contains(text(), 'Date Posted:')]")
                if posted_elements:
                    posted_text = posted_elements[0].text
                    job_data.posted_date = posted_text.replace('Date Posted:', '').strip()
            except:
                pass

            # Extract job ID
            try:
                job_id_elements = self.driver.find_elements(By.XPATH, "//p[contains(text(), 'Job ID:')]")
                if job_id_elements:
                    job_id_text = job_id_elements[0].text
                    job_data.job_id = job_id_text.replace('Job ID:', '').strip()
            except:
                pass

            # Extract role
            try:
                role_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_role'])
                job_data.role = role_element.text.strip()
            except:
                pass

            # Extract industry
            try:
                industry_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_industry'])
                job_data.industry = industry_element.text.strip()
            except:
                pass

            # Extract function
            try:
                function_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_function'])
                job_data.function = function_element.text.strip()
            except:
                pass

            # Extract job type
            try:
                job_type_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['job_detail_job_type'])
                job_data.job_type = job_type_element.text.strip()
            except:
                pass

            # Extract company description
            try:
                company_desc_element = self.driver.find_element(By.CSS_SELECTOR, self.selectors['company_description'])
                job_data.company_description = company_desc_element.text.strip()
            except:
                pass

            # Extract applicants count
            try:
                applicants_elements = self.driver.find_elements(By.XPATH, "//span[contains(text(), 'applicants')]")
                if applicants_elements:
                    job_data.applicants_count = applicants_elements[0].text.strip()
            except:
                pass

        except Exception as e:
            logger.debug(f"Error extracting additional details: {e}")

    def extract_job_data_from_javascript(self) -> List[dict]:
        """Extract job data from JavaScript variables on the search results page"""
        if not self.driver:
            return []

        try:
            # Wait for JavaScript to load
            self.random_delay(3, 5)

            # Try multiple JavaScript extraction methods
            extraction_scripts = [
                # Method 1: Look for window.__NEXT_DATA__ (Next.js data)
                """
                try {
                    if (window.__NEXT_DATA__) {
                        var nextData = window.__NEXT_DATA__;

                        // Look for job data in various locations within __NEXT_DATA__
                        var jobData = [];

                        // Check props.pageProps
                        if (nextData.props && nextData.props.pageProps) {
                            var pageProps = nextData.props.pageProps;

                            // Look for job arrays
                            if (pageProps.jobs) jobData = jobData.concat(pageProps.jobs);
                            if (pageProps.jobData) jobData = jobData.concat(pageProps.jobData);
                            if (pageProps.jobDataArray) jobData = jobData.concat(pageProps.jobDataArray);
                            if (pageProps.searchResults) jobData = jobData.concat(pageProps.searchResults);
                            if (pageProps.results) jobData = jobData.concat(pageProps.results);

                            // Look for nested data
                            if (pageProps.data && pageProps.data.jobs) jobData = jobData.concat(pageProps.data.jobs);
                            if (pageProps.initialData && pageProps.initialData.jobs) jobData = jobData.concat(pageProps.initialData.jobs);
                        }

                        // Check query data
                        if (nextData.query) {
                            var query = nextData.query;
                            if (query.jobs) jobData = jobData.concat(query.jobs);
                            if (query.results) jobData = jobData.concat(query.results);
                        }

                        // Check buildId data
                        if (nextData.buildId && window['__NEXT_DATA_' + nextData.buildId]) {
                            var buildData = window['__NEXT_DATA_' + nextData.buildId];
                            if (buildData.jobs) jobData = jobData.concat(buildData.jobs);
                        }

                        if (jobData.length > 0) {
                            return jobData;
                        }

                        // Return the entire __NEXT_DATA__ for analysis
                        return nextData;
                    }
                    return null;
                } catch (e) {
                    console.log('Error in __NEXT_DATA__ extraction:', e);
                    return null;
                }
                """,

                # Method 2: Look for job data in script tags (comprehensive search)
                r"""
                try {
                    var jobData = [];
                    var scripts = document.querySelectorAll('script');

                    for (var i = 0; i < scripts.length; i++) {
                        var content = scripts[i].textContent || scripts[i].innerText;
                        if (!content) continue;

                        // Look for various job data patterns
                        try {
                            // Pattern 1: Look for jobData or jobDataArray
                            if (content.includes('jobData') || content.includes('jobDataArray')) {
                                // Try to extract JSON objects containing job data
                                var jsonMatches = content.match(/\{[^{}]*"(?:jobId|id)":\s*(?:\d+|"[^"]+")[^{}]*\}/g);
                                if (jsonMatches) {
                                    for (var j = 0; j < jsonMatches.length; j++) {
                                        try {
                                            var jobObj = JSON.parse(jsonMatches[j]);
                                            if (jobObj.jobId || jobObj.id) {
                                                jobData.push(jobObj);
                                            }
                                        } catch (parseError) {
                                            // Try to extract key-value pairs manually
                                            var idMatch = jsonMatches[j].match(/"(?:jobId|id)":\s*(?:(\d+)|"([^"]+)")/);
                                            var titleMatch = jsonMatches[j].match(/"title":\s*"([^"]+)"/);
                                            var companyMatch = jsonMatches[j].match(/"(?:companyName|company)":\s*"([^"]+)"/);

                                            if (idMatch && titleMatch) {
                                                jobData.push({
                                                    jobId: idMatch[1] || idMatch[2],
                                                    title: titleMatch[1],
                                                    companyName: companyMatch ? companyMatch[1] : ''
                                                });
                                            }
                                        }
                                    }
                                }
                            }

                            // Pattern 2: Look for __NEXT_DATA__ in script content
                            if (content.includes('__NEXT_DATA__')) {
                                var nextDataMatch = content.match(/__NEXT_DATA__\s*=\s*(\{.*?\});?/);
                                if (nextDataMatch) {
                                    try {
                                        var nextData = JSON.parse(nextDataMatch[1]);
                                        if (nextData.props && nextData.props.pageProps) {
                                            var pageProps = nextData.props.pageProps;
                                            if (pageProps.jobs) jobData = jobData.concat(pageProps.jobs);
                                            if (pageProps.jobData) jobData = jobData.concat(pageProps.jobData);
                                            if (pageProps.jobDataArray) jobData = jobData.concat(pageProps.jobDataArray);
                                        }
                                    } catch (nextParseError) {
                                        console.log('Error parsing __NEXT_DATA__:', nextParseError);
                                    }
                                }
                            }

                            // Pattern 3: Look for window assignments
                            if (content.includes('window.') && (content.includes('job') || content.includes('search'))) {
                                var windowMatches = content.match(/window\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*(\{.*?\}|\[.*?\]);?/g);
                                if (windowMatches) {
                                    for (var k = 0; k < windowMatches.length; k++) {
                                        try {
                                            var assignMatch = windowMatches[k].match(/window\.([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*(.*);?/);
                                            if (assignMatch) {
                                                var varName = assignMatch[1];
                                                var varValue = assignMatch[2];

                                                if (varName.toLowerCase().includes('job') || varName.toLowerCase().includes('search')) {
                                                    try {
                                                        var parsedValue = JSON.parse(varValue);
                                                        if (Array.isArray(parsedValue)) {
                                                            jobData = jobData.concat(parsedValue);
                                                        } else if (parsedValue && typeof parsedValue === 'object') {
                                                            jobData.push(parsedValue);
                                                        }
                                                    } catch (windowParseError) {
                                                        // Ignore parse errors for window assignments
                                                    }
                                                }
                                            }
                                        } catch (windowError) {
                                            continue;
                                        }
                                    }
                                }
                            }

                        } catch (scriptError) {
                            console.log('Error processing script:', scriptError);
                            continue;
                        }
                    }

                    return jobData.length > 0 ? jobData : null;
                } catch (e) {
                    console.log('Error in script extraction:', e);
                    return null;
                }
                """,

                # Method 3: Look for job cards with data attributes
                """
                var jobCards = document.querySelectorAll('[data-job-id], [data-jobid], .job-card, .jobCard');
                var jobs = [];
                for (var i = 0; i < jobCards.length; i++) {
                    var card = jobCards[i];
                    var jobId = card.getAttribute('data-job-id') || card.getAttribute('data-jobid');
                    var titleEl = card.querySelector('h1, h2, h3, .job-title, [data-testid="job-title"]');
                    var companyEl = card.querySelector('.company-name, [data-testid="company-name"]');

                    if (jobId || titleEl) {
                        jobs.push({
                            jobId: jobId,
                            title: titleEl ? titleEl.textContent.trim() : '',
                            company: companyEl ? companyEl.textContent.trim() : ''
                        });
                    }
                }
                return jobs.length > 0 ? jobs : null;
                """,

                # Method 4: Extract from any global variables
                """
                var globalVars = ['jobsData', 'searchResults', 'jobResults', 'jobs'];
                for (var i = 0; i < globalVars.length; i++) {
                    if (window[globalVars[i]]) {
                        return window[globalVars[i]];
                    }
                }
                return null;
                """
            ]

            job_data = []

            for i, script in enumerate(extraction_scripts):
                try:
                    logger.info(f"Trying JavaScript extraction method {i+1}")
                    result = self.driver.execute_script(script)

                    if result:
                        logger.info(f"Method {i+1} returned data: {type(result)}")
                        if isinstance(result, list):
                            job_data.extend(result)
                        elif isinstance(result, dict):
                            # Try to find job data in the dict
                            if 'jobs' in result:
                                job_data.extend(result['jobs'])
                            elif 'searchResults' in result:
                                job_data.extend(result['searchResults'])
                            else:
                                job_data.append(result)

                        if job_data:
                            break

                except Exception as e:
                    logger.debug(f"JavaScript method {i+1} failed: {e}")
                    continue

            logger.info(f"Extracted {len(job_data)} job records from JavaScript")
            if job_data:
                logger.info(f"Sample job data: {job_data[0] if job_data else 'None'}")

            return job_data

        except Exception as e:
            logger.error(f"Error extracting job data from JavaScript: {e}")
            return []

    def construct_job_url_from_data(self, job_data: dict) -> str:
        """Construct job URL from job data using the known pattern"""
        try:
            job_id = job_data.get('jobId') or job_data.get('id')
            title = job_data.get('title', '')
            company = job_data.get('company', '') or job_data.get('companyName', '')
            location = job_data.get('location', '') or job_data.get('city', '')

            if not job_id:
                return ""

            # Clean and format URL components
            def clean_for_url(text):
                if not text:
                    return ""
                # Convert to lowercase, replace spaces and special chars with hyphens
                import re
                text = re.sub(r'[^\w\s-]', '', text.lower())
                text = re.sub(r'[\s_]+', '-', text)
                text = re.sub(r'-+', '-', text)  # Remove multiple consecutive hyphens
                return text.strip('-')

            title_clean = clean_for_url(title)
            company_clean = clean_for_url(company)
            location_clean = clean_for_url(location)

            # Construct URL: /job/{title-company-location-jobid}
            url_parts = [part for part in [title_clean, company_clean, location_clean] if part]
            url_slug = '-'.join(url_parts) + f'-{job_id}'

            job_url = f"https://www.foundit.in/job/{url_slug}"

            logger.info(f"Constructed job URL: {job_url}")
            return job_url

        except Exception as e:
            logger.error(f"Error constructing job URL: {e}")
            return ""

    def extract_job_urls_from_page_source(self) -> List[str]:
        """Extract job URLs using JavaScript execution and URL construction"""
        if not self.driver:
            return []

        try:
            # First try to extract job data from JavaScript
            job_data_list = self.extract_job_data_from_javascript()

            job_urls = []

            if job_data_list:
                logger.info(f"Found {len(job_data_list)} job records from JavaScript")

                for job_data in job_data_list:
                    job_url = self.construct_job_url_from_data(job_data)
                    if job_url and job_url not in job_urls:
                        job_urls.append(job_url)

            # Fallback: Try regex patterns on page source
            if not job_urls:
                logger.info("JavaScript extraction failed, trying regex patterns")
                page_source = self.driver.page_source

                import re

                # Look for job IDs in various patterns
                job_id_patterns = [
                    r'"jobId":\s*(\d+)',
                    r'"id":\s*"(\d+)"',
                    r'data-job-id="(\d+)"',
                    r'/job/[^"\'>\s]*-(\d+)',
                ]

                job_ids = set()
                for pattern in job_id_patterns:
                    matches = re.findall(pattern, page_source)
                    job_ids.update(matches)

                logger.info(f"Found {len(job_ids)} unique job IDs from regex")

                # For now, we can't construct full URLs without more data
                # But we can try basic patterns
                if job_ids:
                    logger.info(f"Sample job IDs: {list(job_ids)[:5]}")

            logger.info(f"Extracted {len(job_urls)} job URLs total")
            if job_urls:
                logger.info(f"Sample URLs: {job_urls[:3]}")

            return job_urls

        except Exception as e:
            logger.error(f"Error extracting job URLs from page source: {e}")
            return []

    def navigate_to_job_with_session(self, job_url: str) -> bool:
        """Navigate to job URL with proper session handling"""
        if not self.driver:
            return False

        try:
            # Store current page URL as referrer
            referrer_url = self.driver.current_url

            # Navigate to job URL
            logger.info(f"Navigating to job URL: {job_url}")
            self.driver.get(job_url)

            # Wait for page to load
            self.random_delay(3, 5)

            # Check if we successfully loaded the job page
            try:
                # Look for job detail indicators
                job_indicators = [
                    '#jobDescription',
                    '.job-description-content',
                    'h1',
                    '[data-testid="job-title"]'
                ]

                for indicator in job_indicators:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    if elements:
                        logger.info(f"Successfully loaded job page - found {indicator}")
                        return True

                # Check if we got redirected or blocked
                current_url = self.driver.current_url
                if 'access-denied' in current_url or 'error' in current_url:
                    logger.warning(f"Access denied or error page detected: {current_url}")
                    return False

                logger.info("Job page loaded successfully")
                return True

            except Exception as e:
                logger.warning(f"Could not verify job page load: {e}")
                return True  # Assume success if we can't verify

        except Exception as e:
            logger.error(f"Error navigating to job URL: {e}")
            return False

    def extract_job_id_from_card(self, card_element) -> str:
        """Try to extract job ID from a job card element"""
        try:
            # Method 1: Look for data attributes
            for attr in ['data-job-id', 'data-jobid', 'data-id']:
                job_id = card_element.get_attribute(attr)
                if job_id:
                    logger.info(f"Found job ID from {attr}: {job_id}")
                    return job_id

            # Method 2: Look for job ID in onclick handlers or other attributes
            onclick = card_element.get_attribute('onclick')
            if onclick and 'job' in onclick.lower():
                import re
                job_id_match = re.search(r'(\d{8,})', onclick)
                if job_id_match:
                    logger.info(f"Found job ID from onclick: {job_id_match.group(1)}")
                    return job_id_match.group(1)

            # Method 3: Look for job ID in href attributes
            links = card_element.find_elements(By.CSS_SELECTOR, 'a[href]')
            for link in links:
                href = link.get_attribute('href')
                if href:
                    import re
                    job_id_match = re.search(r'/job/[^/]*-(\d{8,})', href)
                    if job_id_match:
                        logger.info(f"Found job ID from href: {job_id_match.group(1)}")
                        return job_id_match.group(1)

            # Method 4: Look for job ID in the card's HTML
            card_html = card_element.get_attribute('outerHTML')
            if card_html:
                import re
                job_id_matches = re.findall(r'(\d{8,})', card_html)
                if job_id_matches:
                    # Take the first 8+ digit number found
                    logger.info(f"Found job ID from HTML: {job_id_matches[0]}")
                    return job_id_matches[0]

            return ""

        except Exception as e:
            logger.debug(f"Error extracting job ID from card: {e}")
            return ""

    def extract_job_data_from_card(self, card_element) -> JobData:
        """Extract basic job information from a job card on search results page"""
        job_data = JobData()

        try:
            # Extract job ID first
            job_id = self.extract_job_id_from_card(card_element)
            if job_id:
                job_data.job_id = job_id

            # Extract job title
            try:
                title_elements = card_element.find_elements(By.CSS_SELECTOR, 'h3, .job-title, [data-testid="job-title"]')
                if title_elements:
                    job_data.title = title_elements[0].text.strip()
            except:
                pass

            # Extract company name
            try:
                company_elements = card_element.find_elements(By.CSS_SELECTOR, 'a[href*="jobs-career"], .company-name, span p')
                if company_elements:
                    job_data.company_name = company_elements[0].text.strip()
            except:
                pass

            # Extract location
            try:
                location_elements = card_element.find_elements(By.CSS_SELECTOR, 'a[href*="jobs-in-"], .location')
                if location_elements:
                    job_data.location = location_elements[0].text.strip()
            except:
                pass

            # Extract experience
            try:
                exp_elements = card_element.find_elements(By.CSS_SELECTOR, 'span, label')
                for elem in exp_elements:
                    text = elem.text.strip()
                    if 'year' in text.lower() or 'exp' in text.lower():
                        job_data.experience = text
                        break
            except:
                pass

            # Try to construct job URL if we have job ID
            if job_data.job_id and job_data.title and job_data.company_name:
                constructed_url = self.construct_job_url_from_data({
                    'jobId': job_data.job_id,
                    'title': job_data.title,
                    'company': job_data.company_name,
                    'location': job_data.location
                })
                if constructed_url:
                    job_data.job_url = constructed_url
                    logger.info(f"Constructed job URL: {constructed_url}")

            # Fallback: Extract any links that might be job URLs
            if not job_data.job_url:
                try:
                    link_elements = card_element.find_elements(By.CSS_SELECTOR, 'a[href]')
                    for link in link_elements:
                        href = link.get_attribute('href')
                        if href and 'job' in href and 'foundit.in' in href:
                            job_data.job_url = href
                            break
                except:
                    pass

            # Set a basic job URL if none found
            if not job_data.job_url:
                job_data.job_url = self.driver.current_url if self.driver else ""

            logger.info(f"Extracted from card - Title: {job_data.title}, Company: {job_data.company_name}, ID: {job_data.job_id}")
            return job_data

        except Exception as e:
            logger.error(f"Error extracting job data from card: {e}")
            return job_data

    def navigate_back_to_results(self):
        """Navigate back to search results page"""
        try:
            if not self.driver or not self.wait:
                logger.error("Driver or wait not initialized")
                raise Exception("Driver not initialized")

            self.driver.back()
            self.random_delay(2, 3)

            # Wait for job cards to be present again
            self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors['job_cards']))
            )
            logger.info("Successfully navigated back to search results")

        except Exception as e:
            logger.error(f"Failed to navigate back to results: {e}")
            raise

    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict:
        """Main method to scrape jobs following the specified workflow"""
        scraped_jobs = []

        try:
            # Step 1: Setup driver
            logger.info(f"Starting job scraping for '{job_title}' in '{location}' - Target: {num_jobs} jobs")
            self.setup_driver()

            # Step 2: Navigate to homepage
            if not self.navigate_to_homepage():
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': 'Failed to navigate to homepage'}

            # Step 2.5: Inject network monitoring early
            self.inject_network_monitoring()

            # Step 3: Handle cookie consent
            if not self.handle_cookie_consent():
                logger.warning("Cookie consent handling failed, continuing...")

            # Step 4: Fill search form
            if not self.fill_search_form(job_title, location):
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': 'Failed to fill search form'}

            # Step 5: Wait for search results
            if not self.wait_for_search_results():
                return {'scraped_jobs': [], 'total_scraped': 0, 'error': 'Search results did not load'}

            # Step 6: Debug - Save page source for analysis
            try:
                if self.driver:
                    page_source = self.driver.page_source
                    with open('debug_search_results.html', 'w', encoding='utf-8') as f:
                        f.write(page_source)
                    logger.info("Saved page source to debug_search_results.html for analysis")
            except Exception as e:
                logger.debug(f"Could not save page source: {e}")

            # Step 7: Capture network logs to get job data from AJAX calls
            logger.info("Capturing network logs for job data...")
            network_logs = self.capture_network_logs()

            # Also get data from injected monitoring
            captured_network_data = self.get_captured_network_data()
            logger.info(f"Retrieved {len(captured_network_data)} network events from injected monitoring")

            # Extract job data from network responses
            network_job_data = self.network_interceptor.extract_job_data_from_responses()

            # Debug: Save JavaScript execution results for analysis
            self.debug_javascript_data()

            if network_job_data:
                logger.info(f"Found {len(network_job_data)} jobs from network interception")

                # Process network job data
                jobs_to_process = network_job_data[:num_jobs]
                for i, job_data in enumerate(jobs_to_process):
                    try:
                        logger.info(f"Processing network job {i+1}/{len(jobs_to_process)}")

                        # Convert network job data to our JobData format
                        processed_job = self.convert_network_job_data(job_data)
                        if processed_job.title:
                            scraped_jobs.append(asdict(processed_job))
                            logger.info(f"Successfully processed network job: {processed_job.title}")

                    except Exception as e:
                        logger.error(f"Error processing network job {i+1}: {e}")
                        continue

            # Step 8: Fallback to traditional extraction if network interception didn't work
            if not scraped_jobs:
                logger.info("Network interception didn't capture job data, falling back to traditional extraction")

                job_urls = self.extract_job_urls_from_page_source()
                if job_urls:
                    # Process job URLs directly
                    urls_to_process = job_urls[:num_jobs]
                    logger.info(f"Processing {len(urls_to_process)} job URLs")

                    for i, job_url in enumerate(urls_to_process):
                        try:
                            logger.info(f"Processing job {i+1}/{len(urls_to_process)}: {job_url}")

                            # Navigate to the job detail page
                            if self.navigate_to_job_with_session(job_url):
                                # Extract detailed job data from the job page
                                job_data = self.extract_job_detail_data()
                                job_data.job_url = job_url  # Set the actual job URL

                                if job_data.title:
                                    scraped_jobs.append(asdict(job_data))
                                    logger.info(f"Successfully scraped detailed job: {job_data.title}")
                                else:
                                    logger.warning(f"No job title found for URL: {job_url}")
                            else:
                                logger.warning(f"Failed to navigate to job URL: {job_url}")

                            # Add delay between job processing
                            if i < len(urls_to_process) - 1:
                                self.random_delay(2, 4)

                        except Exception as e:
                            logger.error(f"Error processing job URL {i+1}: {e}")
                            continue
                else:
                    logger.warning("No job URLs found in page source, falling back to card extraction")
                    # Fallback to card-based extraction
                    job_cards = self.get_job_cards()
                    if not job_cards:
                        return {'scraped_jobs': [], 'total_scraped': 0, 'error': 'No job cards or URLs found'}

                    cards_to_process = job_cards[:num_jobs]
                    for i, card in enumerate(cards_to_process):
                        try:
                            logger.info(f"Processing job card {i+1}/{len(cards_to_process)}")
                            job_data = self.extract_job_data_from_card(card)

                            if job_data.title:
                                scraped_jobs.append(asdict(job_data))
                                logger.info(f"Successfully scraped job from card: {job_data.title}")
                        except Exception as e:
                            logger.error(f"Error processing job card {i+1}: {e}")
                            continue

            logger.info(f"Scraping completed. Successfully scraped {len(scraped_jobs)} jobs")
            return {
                'scraped_jobs': scraped_jobs,
                'total_scraped': len(scraped_jobs),
                'requested': num_jobs,
                'success': True
            }

        except Exception as e:
            logger.error(f"Fatal error during scraping: {e}")
            return {
                'scraped_jobs': scraped_jobs,
                'total_scraped': len(scraped_jobs),
                'error': str(e),
                'success': False
            }

        finally:
            self.cleanup()

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Driver closed successfully")
            except Exception as e:
                logger.warning(f"Error closing driver: {e}")

# FastAPI endpoints
@app.post("/scrape")
async def scrape_jobs_endpoint(request: JobSearchRequest):
    """
    Scrape jobs from Foundit.in following the specified workflow

    Workflow:
    1. Navigate to https://www.foundit.in/
    2. Handle cookie consent banner
    3. Fill search form with job title and location
    4. Process job results by clicking on job cards
    5. Extract detailed job information from individual job pages
    """
    try:
        logger.info(f"Received scraping request: {request.job_title} in {request.location}, {request.num_jobs} jobs")

        # Validate inputs
        if not request.job_title or not request.job_title.strip():
            raise HTTPException(status_code=400, detail="Job title is required")
        if not request.location or not request.location.strip():
            raise HTTPException(status_code=400, detail="Location is required")
        if request.num_jobs <= 0 or request.num_jobs > 50:
            raise HTTPException(status_code=400, detail="Number of jobs must be between 1 and 50")

        # Create scraper instance (temporarily non-headless for debugging)
        scraper = FounditScraperV2(headless=False)

        # Scrape jobs
        result = scraper.scrape_jobs(
            job_title=request.job_title.strip(),
            location=request.location.strip(),
            num_jobs=request.num_jobs
        )

        return JSONResponse(content=result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in scrape endpoint: {e}")
        return JSONResponse(
            status_code=500,
            content={
                'scraped_jobs': [],
                'total_scraped': 0,
                'error': f"Internal server error: {str(e)}",
                'success': False
            }
        )

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Foundit Job Scraper v2.0",
        "description": "Enhanced Foundit scraper following specified workflow",
        "workflow": [
            "1. Navigate to https://www.foundit.in/",
            "2. Handle cookie consent banner",
            "3. Fill search form with job title and location",
            "4. Process job results by clicking on job cards",
            "5. Extract detailed job information from individual job pages"
        ],
        "endpoints": {
            "/scrape": "POST - Main scraping endpoint",
            "/health": "GET - Health check",
            "/": "GET - API information"
        },
        "status": "active"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "foundit-scraper-v2",
        "version": "2.0"
    }

if __name__ == "__main__":
    logger.info("Starting Foundit Job Scraper v2.0...")
    uvicorn.run(app, host="0.0.0.0", port=8002)
