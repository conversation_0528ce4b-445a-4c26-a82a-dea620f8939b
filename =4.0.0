Collecting DrissionPage
  Obtaining dependency information for DrissionPage from https://files.pythonhosted.org/packages/e6/83/7ed71f4e0c8d60c9aefb24f0bb95f8a8b14e090718cf3fff8a0083d33164/DrissionPage-********-py3-none-any.whl.metadata
  Downloading DrissionPage-********-py3-none-any.whl.metadata (4.7 kB)
Collecting lxml (from DrissionPage)
  Obtaining dependency information for lxml from https://files.pythonhosted.org/packages/7c/23/828d4cc7da96c611ec0ce6147bbcea2fdbde023dc995a165afa512399bbf/lxml-6.0.0-cp311-cp311-macosx_10_9_universal2.whl.metadata
  Downloading lxml-6.0.0-cp311-cp311-macosx_10_9_universal2.whl.metadata (6.6 kB)
Requirement already satisfied: requests in ./venv/lib/python3.11/site-packages (from DrissionPage) (2.32.4)
Collecting cssselect (from DrissionPage)
  Obtaining dependency information for cssselect from https://files.pythonhosted.org/packages/ee/58/257350f7db99b4ae12b614a36256d9cc870d71d9e451e79c2dc3b23d7c3c/cssselect-1.3.0-py3-none-any.whl.metadata
  Downloading cssselect-1.3.0-py3-none-any.whl.metadata (2.6 kB)
Collecting DownloadKit>=2.0.7 (from DrissionPage)
  Obtaining dependency information for DownloadKit>=2.0.7 from https://files.pythonhosted.org/packages/1b/89/979ad2407eb8e0a2ac67461b7bec7db8627218fe3ba287db49636ca0a60c/DownloadKit-2.0.7-py3-none-any.whl.metadata
  Downloading DownloadKit-2.0.7-py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: websocket-client in ./venv/lib/python3.11/site-packages (from DrissionPage) (1.8.0)
Requirement already satisfied: click in ./venv/lib/python3.11/site-packages (from DrissionPage) (8.2.1)
Collecting tldextract>=3.4.4 (from DrissionPage)
  Obtaining dependency information for tldextract>=3.4.4 from https://files.pythonhosted.org/packages/67/7c/ea488ef48f2f544566947ced88541bc45fae9e0e422b2edbf165ee07da99/tldextract-5.3.0-py3-none-any.whl.metadata
  Downloading tldextract-5.3.0-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: psutil in ./venv/lib/python3.11/site-packages (from DrissionPage) (7.0.0)
Collecting DataRecorder>=3.4.11 (from DownloadKit>=2.0.7->DrissionPage)
  Obtaining dependency information for DataRecorder>=3.4.11 from https://files.pythonhosted.org/packages/33/05/f0064d93a0b922ea7ad5d40a0dfaac45de37f783a85a2900e6ce8a01dbc3/DataRecorder-3.6.2-py3-none-any.whl.metadata
  Downloading DataRecorder-3.6.2-py3-none-any.whl.metadata (4.5 kB)
Requirement already satisfied: idna in ./venv/lib/python3.11/site-packages (from tldextract>=3.4.4->DrissionPage) (3.10)
Collecting requests-file>=1.4 (from tldextract>=3.4.4->DrissionPage)
  Obtaining dependency information for requests-file>=1.4 from https://files.pythonhosted.org/packages/d7/25/dd878a121fcfdf38f52850f11c512e13ec87c2ea72385933818e5b6c15ce/requests_file-2.1.0-py2.py3-none-any.whl.metadata
  Downloading requests_file-2.1.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting filelock>=3.0.8 (from tldextract>=3.4.4->DrissionPage)
  Obtaining dependency information for filelock>=3.0.8 from https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl.metadata
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.11/site-packages (from requests->DrissionPage) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.11/site-packages (from requests->DrissionPage) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.11/site-packages (from requests->DrissionPage) (2025.7.14)
Collecting openpyxl (from DataRecorder>=3.4.11->DownloadKit>=2.0.7->DrissionPage)
  Obtaining dependency information for openpyxl from https://files.pythonhosted.org/packages/c0/da/977ded879c29cbd04de313843e76868e6e13408a94ed6b987245dc7c8506/openpyxl-3.1.5-py2.py3-none-any.whl.metadata
  Downloading openpyxl-3.1.5-py2.py3-none-any.whl.metadata (2.5 kB)
Collecting et-xmlfile (from openpyxl->DataRecorder>=3.4.11->DownloadKit>=2.0.7->DrissionPage)
  Obtaining dependency information for et-xmlfile from https://files.pythonhosted.org/packages/c1/8b/5fe2cc11fee489817272089c4203e679c63b570a5aaeb18d852ae3cbba6a/et_xmlfile-2.0.0-py3-none-any.whl.metadata
  Downloading et_xmlfile-2.0.0-py3-none-any.whl.metadata (2.7 kB)
Downloading DrissionPage-********-py3-none-any.whl (256 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━ 256.9/256.9 kB 1.7 MB/s eta 0:00:00
Downloading DownloadKit-2.0.7-py3-none-any.whl (21 kB)
Downloading tldextract-5.3.0-py3-none-any.whl (107 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━ 107.4/107.4 kB 7.4 MB/s eta 0:00:00
Downloading cssselect-1.3.0-py3-none-any.whl (18 kB)
Downloading lxml-6.0.0-cp311-cp311-macosx_10_9_universal2.whl (8.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━ 8.4/8.4 MB 2.0 MB/s eta 0:00:00
Downloading DataRecorder-3.6.2-py3-none-any.whl (37 kB)
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Downloading requests_file-2.1.0-py2.py3-none-any.whl (4.2 kB)
Downloading openpyxl-3.1.5-py2.py3-none-any.whl (250 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━ 250.9/250.9 kB 2.1 MB/s eta 0:00:00
Downloading et_xmlfile-2.0.0-py3-none-any.whl (18 kB)
Installing collected packages: lxml, filelock, et-xmlfile, cssselect, requests-file, openpyxl, tldextract, DataRecorder, DownloadKit, DrissionPage
Successfully installed DataRecorder-3.6.2 DownloadKit-2.0.7 DrissionPage-******** cssselect-1.3.0 et-xmlfile-2.0.0 filelock-3.18.0 lxml-6.0.0 openpyxl-3.1.5 requests-file-2.1.0 tldextract-5.3.0
