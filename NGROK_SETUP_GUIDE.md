# Job Portal ngrok Setup Guide

This guide explains how to expose your Job Portal application through ngrok tunnels for public access.

## 🚀 Quick Start (Backend Only)

The simplest way to get started is with the backend-only setup:

```bash
./start-backend-only.sh
```

This will:
1. Activate the Python virtual environment
2. Start the FastAPI backend on port 8000
3. Create an ngrok tunnel to expose the backend publicly
4. Display the public URL for accessing your API

## 📋 Prerequisites

- Python virtual environment (`.venv`) with all dependencies installed
- ngrok installed (automatically downloaded if not present)
- Internet connection for ngrok tunnel

## 🛠️ Available Setup Scripts

### 1. Backend Only (`start-backend-only.sh`)
**Recommended for API testing and development**

```bash
./start-backend-only.sh
```

**What it does:**
- Starts FastAPI backend on port 8000
- Creates ngrok tunnel for backend
- Provides public URL for API access

**Output example:**
```
🎉 Backend setup complete!

📱 Access your backend API:
   🌐 Public URL:     https://b5da3083a912.ngrok-free.app
   🏠 Local URL:      http://localhost:8000

🔗 API Endpoints (via tunnel):
   📊 Health:         https://b5da3083a912.ngrok-free.app/health
   📚 API Docs:       https://b5da3083a912.ngrok-free.app/docs
```

### 2. Simple Setup (`start-simple.sh`)
**For full frontend + backend setup (requires Node.js)**

```bash
./start-simple.sh
```

**What it does:**
- Starts both frontend (port 3000) and backend (port 8000)
- Sets up nginx proxy (if available) on port 8080
- Creates ngrok tunnel for the proxy or backend

### 3. Docker Setup (`start-with-docker.sh`)
**For containerized deployment**

```bash
./start-with-docker.sh
```

**What it does:**
- Builds and runs Docker containers for all services
- Sets up nginx reverse proxy
- Creates ngrok tunnel for the complete application

## 🔗 API Endpoints

Once your backend is running through ngrok, you can access these endpoints:

### Core Endpoints
- **Health Check**: `{NGROK_URL}/health`
- **API Documentation**: `{NGROK_URL}/docs`
- **Alternative Docs**: `{NGROK_URL}/redoc`

### Job Scraper Endpoints
- **Foundit**: `{NGROK_URL}/foundit/scrape_foundit`
- **Glassdoor**: `{NGROK_URL}/glassdoor/scrape_jobs_parallel`
- **SimplyHired**: `{NGROK_URL}/simplyhired/scrape_simplyhired`
- **ZipRecruiter**: `{NGROK_URL}/ziprecruiter/scrape_ziprecruiter`
- **LinkedIn**: `{NGROK_URL}/linkedin/scrape-linkedin/`
- **Indeed**: `{NGROK_URL}/scrape-indeed/`
- **Naukri**: `{NGROK_URL}/naukri/scrape-naukri/`

## 🧪 Testing Your Setup

### 1. Test Health Endpoint
```bash
curl https://your-ngrok-url.ngrok-free.app/health
```

### 2. Test a Job Scraper
```bash
curl -X POST https://your-ngrok-url.ngrok-free.app/foundit/scrape_foundit \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 3
  }'
```

### 3. Access Interactive API Docs
Open in browser: `https://your-ngrok-url.ngrok-free.app/docs`

## 📊 Monitoring

### ngrok Web Interface
- URL: `http://localhost:4040`
- Shows tunnel status, request logs, and traffic

### Service Logs
- Backend logs appear in the terminal where you ran the script
- Use `Ctrl+C` to stop all services

## 🔧 Configuration Files

### nginx.conf
- Reverse proxy configuration for routing traffic
- Routes API calls to backend, frontend calls to Next.js

### docker-compose.yml
- Orchestrates all services in containers
- Includes nginx, frontend, and backend services

### Dockerfiles
- `web-portal/Dockerfile`: Frontend container
- `Dockerfile.backend`: Backend container with Chrome for scraping

## 🚨 Troubleshooting

### Backend Won't Start
```bash
# Check if port 8000 is in use
lsof -i :8000

# Start backend manually to see errors
source .venv/bin/activate
uvicorn main:app --reload --port 8000
```

### ngrok Issues
```bash
# Check ngrok status
curl http://localhost:4040/api/tunnels

# Restart ngrok manually
ngrok http 8000
```

### Frontend Issues (Node.js required)
```bash
# Install Node.js dependencies
cd web-portal
npm install
npm run dev
```

## 🔒 Security Notes

- ngrok URLs are publicly accessible
- Don't expose sensitive data through the tunnel
- Use ngrok's authentication features for production
- Monitor the ngrok web interface for unexpected traffic

## 🛑 Stopping Services

Press `Ctrl+C` in the terminal where you started the script. This will:
1. Stop the ngrok tunnel
2. Stop the backend server
3. Stop any other running services
4. Clean up background processes

## 💡 Tips

1. **Bookmark the ngrok URL** - it changes each time you restart
2. **Use the API docs** - `{NGROK_URL}/docs` for interactive testing
3. **Monitor requests** - Check `http://localhost:4040` for traffic
4. **Test locally first** - Use `http://localhost:8000` before ngrok
5. **Check logs** - Terminal output shows detailed information

## 🔄 Next Steps

1. **Frontend Setup**: Install Node.js to use the complete web interface
2. **Custom Domain**: Configure ngrok with a custom domain
3. **Authentication**: Add ngrok authentication for security
4. **Production**: Consider proper hosting for production use
