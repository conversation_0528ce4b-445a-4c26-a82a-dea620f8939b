# Deployment Guide: Free & Global Options

This guide covers the best **fully free** deployment options for your project, which consists of:

- Python FastAPI backend (multiple scrapers)
- Next.js (React) frontend (in `web-portal/`)

All options below allow **global access** and do not require a credit card.

---

## ⚠️ Important: Long-Running API Calls

> **If your FastAPI endpoints take longer than 1 minute (e.g., 3–4 minutes for scraping), DO NOT use Vercel or Netlify for your backend.**
>
> Use **Railway**, **fly.io**, or **Deta Space** for your backend, as they support long-running HTTP requests.

---

## 1. [Render.com](https://render.com) (Recommended for FastAPI APIs ≤ 15 min)

- **Type:** Free cloud hosting for web services and static sites
- **Global access:** Yes
- **Custom domains:** Yes (with verification)
- **Limits:** 750 hours/month per service, sleeps after 15 min inactivity
- **Timeout:** 15 minutes per request

### Deploy FastAPI Backend

1. Push your code to GitHub.
2. Sign up at [Render](https://render.com/).
3. Click **New +** → **Web Service**.
4. Connect your GitHub repo.
5. Set **Build Command:** `pip install -r requirements.txt`
6. Set **Start Command:** `uvicorn main:app --host 0.0.0.0 --port 10000`
7. Set **Environment:** Python 3.10+ (choose in settings)
8. Expose port `10000` (or as required).
9. Click **Create Web Service**.

> **Note:** For Selenium/Chromedriver scrapers, Render's free tier does **not** support running browsers. Use it for API-only endpoints (e.g., JobSpy-based scrapers). For browser-based scrapers, see **Railway** or **fly.io** below.
> **Note:** Render will terminate any request that runs longer than 15 minutes.

### Deploy Next.js Frontend

1. In Render, click **New +** → **Static Site**.
2. Connect your repo, set root to `web-portal/`.
3. **Build Command:** `npm install && npm run build`
4. **Publish Directory:** `web-portal/out` (for static export) or `web-portal/.next` (for SSR, but SSR is not supported on free static sites).
5. Click **Create Static Site**.

---

## 2. [Vercel](https://vercel.com) (Best for Next.js Frontend, **Not for Long-Running APIs**)

- **Type:** Free static/SSR hosting for Next.js
- **Global access:** Yes
- **Custom domains:** Yes
- **Limits:** 100GB bandwidth/month, 1GB serverless function execution/month
- **Timeout:** **1 minute (60 seconds) for serverless functions**

### Deploy Next.js Frontend

1. Push your code to GitHub.
2. Sign up at [Vercel](https://vercel.com/).
3. Import your repo, set root to `web-portal/`.
4. Vercel auto-detects Next.js and deploys.
5. Set environment variables if needed.
6. Get a global HTTPS URL.

> **WARNING:** Vercel is **not suitable for backend APIs that run longer than 1 minute**. All serverless/API routes will be killed after 60 seconds. Use Vercel for frontend only.

---

## 3. [Railway](https://railway.app) (**Recommended for Long-Running FastAPI APIs**)

- **Type:** Free cloud hosting for web services
- **Global access:** Yes
- **Custom domains:** Yes
- **Limits:** 500 hours/month, 1GB RAM, 1GB disk, sleeps after inactivity
- **Timeout:** **No enforced HTTP timeout** (subject to client/browser limits)

### Deploy FastAPI Backend (including Selenium/Chromedriver)

1. Push your code to GitHub.
2. Sign up at [Railway](https://railway.app/).
3. Click **New Project** → **Deploy from GitHub repo**.
4. Set **Start Command:** `uvicorn main:app --host 0.0.0.0 --port 8000`
5. Add `requirements.txt` for Python dependencies.
6. For Selenium, add install steps for `chromedriver` in a `Dockerfile` or Railway's **Nixpacks**.
7. Expose port `8000`.
8. Deploy and get a global URL.

---

## 4. [fly.io](https://fly.io) (**Recommended for Long-Running FastAPI APIs**)

- **Type:** Free Docker-based hosting (runs almost anything)
- **Global access:** Yes
- **Custom domains:** Yes
- **Limits:** 3 shared CPUs, 256MB RAM, 3GB storage, 160 hours/month free
- **Timeout:** **No enforced HTTP timeout** (subject to client/browser limits)

### Deploy FastAPI Backend (with browser support)

1. Install [flyctl](https://fly.io/docs/hands-on/install/).
2. Run `fly launch` in your project root (accept defaults, set port to 8000).
3. Edit `fly.toml` as needed.
4. For Selenium, use a custom `Dockerfile` that installs `chromedriver` and `undetected-chromedriver`.
5. Run `fly deploy`.
6. Get a global HTTPS URL.

---

## 5. [Glitch.com](https://glitch.com) (Quickest for Demos)

- **Type:** Free instant hosting for Node.js, Python, static sites
- **Global access:** Yes
- **Custom domains:** No (only subdomains)
- **Limits:** 4000 requests/hour, sleeps after 5 min inactivity
- **Timeout:** No enforced HTTP timeout, but not reliable for production

### Deploy FastAPI Backend

1. Create a new Glitch project → **Import from GitHub**.
2. Add a `start.sh` script to run `uvicorn main:app --host 0.0.0.0 --port 3000`.
3. Glitch auto-installs from `requirements.txt`.
4. Get a public URL.

---

## 6. [Deta Space](https://deta.space) (**Good for Long-Running APIs, with resource limits**)

- **Type:** Free micro VM hosting (Python, Node.js, etc.)
- **Global access:** Yes
- **Custom domains:** Yes
- **Limits:** 512MB RAM, 1GB storage, 1M requests/month
- **Timeout:** No enforced HTTP timeout (may be limited by RAM/CPU)

### Deploy FastAPI Backend

1. Install [Deta CLI](https://docs.deta.space/docs/cli/install/).
2. Run `deta new` in your backend directory.
3. Follow prompts to deploy.
4. Get a global HTTPS URL.

---

## Notes & Tips

- **Selenium/Chromedriver:** Most free hosts (except Railway, fly.io, Deta) do **not** support running browsers. For browser-based scraping, use Railway, fly.io, or Deta with a custom Dockerfile.
- **API + Frontend:** You can deploy backend and frontend separately (e.g., backend on Railway, frontend on Vercel).
- **Environment Variables:** Set secrets (API keys, etc.) in the platform's dashboard.
- **Custom Domains:** Most platforms allow you to add your own domain for free.
- **Always check platform limits** before production use.

---

## Quick Comparison Table

| Platform   | Backend (FastAPI) | Frontend (Next.js) | Selenium Support | Custom Domain | Free Tier Limits               | Timeout Limit         |
| ---------- | ----------------- | ------------------ | ---------------- | ------------- | ------------------------------ | --------------------- |
| Render     | Yes (API only)    | Yes (static)       | No               | Yes           | 750h/mo, sleeps after 15min    | 15 min/request        |
| Vercel     | No                | Yes (best)         | N/A              | Yes           | 100GB/mo, 1GB serverless       | **1 min/request**     |
| Railway    | Yes               | Yes                | Yes              | Yes           | 500h/mo, 1GB RAM, sleeps       | **No enforced limit** |
| fly.io     | Yes (Docker)      | Yes (Docker)       | Yes              | Yes           | 160h/mo, 256MB RAM             | **No enforced limit** |
| Glitch     | Yes (simple)      | Yes (static)       | No               | No            | 4000 req/hr, sleeps after 5min | No enforced limit     |
| Deta Space | Yes               | Yes                | Yes (limited)    | Yes           | 1M req/mo, 512MB RAM           | No enforced limit     |

---

## Useful Links

- [Render Docs](https://render.com/docs)
- [Vercel Docs](https://vercel.com/docs)
- [Railway Docs](https://docs.railway.app/)
- [fly.io Docs](https://fly.io/docs/)
- [Glitch Docs](https://help.glitch.com/)
- [Deta Docs](https://docs.deta.space/)

---

**Choose the platform that best fits your needs! For most users:**

- **Frontend:** Vercel (best for Next.js)
- **Backend (API only, short jobs):** Render or Railway
- **Backend (long-running or Selenium):** Railway, fly.io, or Deta Space
