#!/usr/bin/env python3
"""
Test script to verify the URL construction logic works within the scraper context
"""

import sys
import json
from new_foundit import Foundit<PERSON><PERSON>raper, JobData

def test_url_construction_methods():
    """Test the URL construction methods directly"""
    print("=== Testing URL Construction Methods ===\n")
    
    # Create a scraper instance (without initializing browser)
    scraper = FounditScraper(headless=True)
    
    # Test data from the HTML files
    test_cases = [
        {
            "title": "Subcon demand For Apple project Data scientist",
            "company": "Fusion Plus Solutions",
            "location": "Hyderabad",
            "expected_job_id": "35536382"  # From the known working URL
        },
        {
            "title": "Data Scientist gTech Ads Solutions",
            "company": "Google Inc",
            "location": "Hyderabad",
            "expected_job_id": None
        },
        {
            "title": "Data Scientist AI Solutions For Electrification",
            "company": "Siemens",
            "location": "Hyderabad",
            "expected_job_id": None
        },
        {
            "title": "Data Scientist ISS",
            "company": "Amazon Development Centre (India) Private Limited",
            "location": "Hyderabad",
            "expected_job_id": "34334876"  # From the raw-each.html
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['title']}")
        print(f"Company: {test_case['company']}")
        print(f"Location: {test_case['location']}")
        
        # Test slug creation
        title_slug = scraper.create_url_slug(test_case['title'])
        company_slug = scraper.create_url_slug(test_case['company'])
        location_slug = scraper.create_enhanced_location_slug(test_case['location'])
        
        print(f"Title Slug: '{title_slug}'")
        print(f"Company Slug: '{company_slug}'")
        print(f"Location Slug: '{location_slug}'")
        
        # Generate job ID
        if test_case['expected_job_id']:
            job_id = test_case['expected_job_id']
            print(f"Using expected Job ID: {job_id}")
        else:
            job_id = scraper.improve_job_id_extraction(None, test_case['title'], test_case['company'])
            if not job_id:
                # Generate pseudo ID as fallback
                import hashlib
                job_string = f"{test_case['title']}-{test_case['company']}".lower()
                hash_object = hashlib.md5(job_string.encode())
                job_id = str(int(hash_object.hexdigest()[:8], 16))
                while len(job_id) < 8:
                    job_id = "1" + job_id
            print(f"Generated Job ID: {job_id}")
        
        # Construct URL
        constructed_url = scraper.construct_job_url(
            test_case['title'],
            test_case['company'],
            test_case['location'],
            job_id
        )
        
        print(f"Constructed URL: {constructed_url}")
        
        # Validate URL
        is_valid = scraper.validate_constructed_url(constructed_url)
        print(f"URL Valid: {is_valid}")
        
        # Check URL pattern
        expected_pattern = f"{title_slug}-{company_slug}-{location_slug}-{job_id}"
        if expected_pattern in constructed_url:
            print("✅ URL pattern is correct")
        else:
            print("❌ URL pattern is incorrect")
            print(f"Expected pattern: {expected_pattern}")
        
        print("-" * 80)
    
    print("\n=== Testing Search Parameter Extraction ===")
    
    # Test search parameter extraction (without browser)
    try:
        search_params = scraper.extract_search_parameters()
        print(f"Generated search parameters: {search_params}")
        
        if 'searchId' in search_params and 'child_search_id' in search_params:
            print("✅ Search parameters generated successfully")
        else:
            print("❌ Search parameters incomplete")
    except Exception as e:
        print(f"❌ Error extracting search parameters: {e}")
    
    print("\n=== Testing JobData URL Assignment ===")
    
    # Test creating JobData objects with constructed URLs
    for i, test_case in enumerate(test_cases, 1):
        job = JobData()
        job.title = test_case['title']
        job.company_name = test_case['company']
        job.location = test_case['location']
        
        # Use expected job ID if available
        if test_case['expected_job_id']:
            job.job_id = test_case['expected_job_id']
        else:
            # Generate pseudo ID
            import hashlib
            job_string = f"{test_case['title']}-{test_case['company']}".lower()
            hash_object = hashlib.md5(job_string.encode())
            job.job_id = str(int(hash_object.hexdigest()[:8], 16))
            while len(job.job_id) < 8:
                job.job_id = "1" + job.job_id
        
        # Construct URL
        job.job_url = scraper.construct_job_url(
            job.title,
            job.company_name,
            job.location,
            job.job_id
        )
        
        print(f"JobData {i}:")
        print(f"  Title: {job.title}")
        print(f"  Company: {job.company_name}")
        print(f"  Location: {job.location}")
        print(f"  Job ID: {job.job_id}")
        print(f"  Job URL: {job.job_url}")
        print(f"  URL Valid: {scraper.validate_constructed_url(job.job_url)}")
        print()
    
    print("=== Test Complete ===")

def test_known_working_urls():
    """Test against known working URLs"""
    print("\n=== Testing Against Known Working URLs ===")
    
    known_urls = [
        "https://www.foundit.in/job/subcon-demand-for-apple-project-data-scientist-fusion-plus-solutions-hyderabad-secunderabad-telangana-35536382?searchId=c3a1249c-b73e-4532-8915-d6017be8bd0f&child_search_id=670e8c89-097f-4e07-a4ca-1e55deb8506d",
        "https://www.foundit.in/job/data-scientist-gtech-ads-solutions-google-inc-hyderabad-secunderabad-telangana-34550285?searchId=c3a1249c-b73e-4532-8915-d6017be8bd0f&child_search_id=670e8c89-097f-4e07-a4ca-1e55deb8506d"
    ]
    
    scraper = FounditScraper(headless=True)
    
    for i, url in enumerate(known_urls, 1):
        print(f"Testing URL {i}: {url}")
        is_valid = scraper.validate_constructed_url(url)
        print(f"Validation result: {is_valid}")
        
        # Extract components
        import re
        pattern = r'https://www\.foundit\.in/job/(.+)-(\d+)\?'
        match = re.search(pattern, url)
        if match:
            path_components = match.group(1)
            job_id = match.group(2)
            print(f"Path components: {path_components}")
            print(f"Job ID: {job_id}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_url_construction_methods()
    test_known_working_urls()
