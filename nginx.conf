events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server host.docker.internal:3000;
    }
    
    upstream backend {
        server host.docker.internal:8000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # Increase client body size for large requests
        client_max_body_size 10M;
        
        # Proxy settings for better performance
        proxy_buffering off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API routes - route to backend
        location /api/ {
            proxy_pass http://backend/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        # Backend health check
        location /health {
            proxy_pass http://backend/health;
        }
        
        # Backend documentation
        location /docs {
            proxy_pass http://backend/docs;
        }
        
        location /redoc {
            proxy_pass http://backend/redoc;
        }
        
        location /openapi.json {
            proxy_pass http://backend/openapi.json;
        }
        
        # Specific scraper endpoints
        location /foundit/ {
            proxy_pass http://backend/foundit/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /glassdoor/ {
            proxy_pass http://backend/glassdoor/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /simplyhired/ {
            proxy_pass http://backend/simplyhired/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /ziprecruiter/ {
            proxy_pass http://backend/ziprecruiter/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /linkedin/ {
            proxy_pass http://backend/linkedin/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /naukri/ {
            proxy_pass http://backend/naukri/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /scrape-linkedin/ {
            proxy_pass http://backend/scrape-linkedin/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /scrape-indeed/ {
            proxy_pass http://backend/scrape-indeed/;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        # Frontend routes - everything else goes to frontend
        location / {
            proxy_pass http://frontend/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_cache_bypass $http_upgrade;
            
            # Next.js specific settings
            proxy_read_timeout 86400;
        }
        
        # Next.js hot reload
        location /_next/webpack-hmr {
            proxy_pass http://frontend/_next/webpack-hmr;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
