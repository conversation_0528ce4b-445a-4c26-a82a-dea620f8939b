import sys
import os
# Add both the current file's directory and its parent to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from modal.mount import Mount
import modal
import os
from modal import Image

# Modal image with Chrome, Chromedriver, and requirements.txt
image = Image.debian_slim().pip_install(
    "selenium",
    "webdriver-manager",
    "requests",
    "beautifulsoup4",
    "uvicorn"
).run_commands(
    "apt-get update",
    "apt-get install -y wget gnupg2 unzip",
    "wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -",
    "echo 'deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main' | tee /etc/apt/sources.list.d/google-chrome.list",
    "apt-get update", 
    "apt-get install -y google-chrome-stable",
    "apt-get install -y xvfb",
)

app = modal.App("job-portal-scrapers")

# Helper to run a FastAPI app with uvicorn
import uvicorn

def run_uvicorn(app, port=8000):
    uvicorn.run(app, host="0.0.0.0", port=port)

# --- Modal Web Endpoint for Unified API ---
@app.function(image=image, timeout=3600)
@modal.asgi_app()
def main_api():
    from main import app as fastapi_app
    return fastapi_app

# (Optional: keep batch/worker functions for internal use, but do not expose as web endpoints)
@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_linkedin_job_detail_scraper():
    from linkedin_job_detail_scraper import app
    run_uvicorn(app, port=8001)

@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_main():
    from main import app
    run_uvicorn(app, port=8000)

@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_naukri_job_detail_scraper():
    from naukri_job_detail_scraper import app
    run_uvicorn(app, port=8002)

@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_foundit():
    from new_foundit import app
    run_uvicorn(app, port=8003)

@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_glassdoor():
    from new_glassdoor import app
    run_uvicorn(app, port=8004)

@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_simplyhired():
    from new_simplyhired import app
    run_uvicorn(app, port=8005)

@app.function(image=image, timeout=3600, mounts=[Mount.from_local_dir('.')])
def run_ziprecruiter():
    from new_ziprecruiter import app
    run_uvicorn(app, port=8006)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Run scrapers locally or on Modal")
    parser.add_argument("scraper", choices=[
        "linkedin", "main", "naukri", "foundit", "glassdoor", "simplyhired", "ziprecruiter"
    ], help="Which scraper to run")
    args = parser.parse_args()
    if args.scraper == "linkedin":
        run_linkedin_job_detail_scraper.local()
    elif args.scraper == "main":
        run_main.local()
    elif args.scraper == "naukri":
        run_naukri_job_detail_scraper.local()
    elif args.scraper == "foundit":
        run_foundit.local()
    elif args.scraper == "glassdoor":
        run_glassdoor.local()
    elif args.scraper == "simplyhired":
        run_simplyhired.local()
    elif args.scraper == "ziprecruiter":
        run_ziprecruiter.local() 