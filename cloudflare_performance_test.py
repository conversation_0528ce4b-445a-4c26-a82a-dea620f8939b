#!/usr/bin/env python3
"""
Performance test script for optimized Cloudflare bypass in Glassdoor scraper.
This script benchmarks the improvements in Cloudflare challenge handling.
"""

import time
import statistics
from new_glassdoor import ScraperConfig, GlassdoorScraper

def test_cloudflare_performance():
    """Test the optimized Cloudflare bypass performance"""
    print("🚀 Cloudflare Performance Optimization Test")
    print("=" * 60)
    
    # Test configurations
    configs = [
        {
            'name': 'Optimized (Recommended)',
            'config': ScraperConfig(
                driver_mode='undetected',
                headless=False,
                cloudflare_timeout=15,  # Reduced from 45s
                cloudflare_max_retries=3,
                progressive_timeout=True,
                fast_challenge_detection=True,
                auto_fallback_drivers=True,
                use_ultra_fast_forms=True,
                typing_speed='very_fast',
            )
        },
        {
            'name': 'Conservative (Fallback)',
            'config': ScraperConfig(
                driver_mode='undetected',
                headless=False,
                cloudflare_timeout=25,  # Moderate timeout
                cloudflare_max_retries=2,
                progressive_timeout=False,
                fast_challenge_detection=True,
                auto_fallback_drivers=False,
                use_ultra_fast_forms=True,
                typing_speed='fast',
            )
        }
    ]
    
    results = {}
    
    for config_info in configs:
        config_name = config_info['name']
        config = config_info['config']
        
        print(f"\n🧪 Testing {config_name} Configuration")
        print("-" * 40)
        print(f"  • Cloudflare timeout: {config.cloudflare_timeout}s")
        print(f"  • Progressive timeout: {config.progressive_timeout}")
        print(f"  • Fast detection: {config.fast_challenge_detection}")
        print(f"  • Auto fallback: {config.auto_fallback_drivers}")
        print(f"  • Max retries: {config.cloudflare_max_retries}")
        
        try:
            scraper = GlassdoorScraper(config)
            
            # Test with small job count for speed
            start_time = time.time()
            result = scraper.scrape_jobs("Data Scientist", "San Francisco", 2)
            end_time = time.time()
            
            execution_time = end_time - start_time
            scraped_jobs = len(result.get('scraped_jobs', []))
            metadata = result.get('metadata', {})
            
            results[config_name] = {
                'execution_time': execution_time,
                'scraped_jobs': scraped_jobs,
                'success_rate': metadata.get('success_rate', 'N/A'),
                'errors': len(metadata.get('errors', [])),
                'status': 'SUCCESS'
            }
            
            print(f"✅ Success!")
            print(f"   Time: {execution_time:.1f}s")
            print(f"   Jobs: {scraped_jobs}")
            print(f"   Success rate: {metadata.get('success_rate', 'N/A')}")
            
        except Exception as e:
            results[config_name] = {
                'execution_time': 0,
                'scraped_jobs': 0,
                'success_rate': '0%',
                'errors': 1,
                'status': 'FAILED',
                'error': str(e)
            }
            
            print(f"❌ Failed: {str(e)}")
    
    # Performance comparison
    print("\n" + "=" * 60)
    print("📊 PERFORMANCE COMPARISON")
    print("=" * 60)
    
    for config_name, result in results.items():
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        print(f"{status_icon} {config_name}")
        print(f"   Time: {result['execution_time']:.1f}s")
        print(f"   Jobs: {result['scraped_jobs']}")
        print(f"   Success: {result['success_rate']}")
        print(f"   Errors: {result['errors']}")
        if result['status'] == 'FAILED':
            print(f"   Error: {result.get('error', 'Unknown')}")
        print()
    
    # Performance analysis
    successful_results = {k: v for k, v in results.items() if v['status'] == 'SUCCESS'}
    
    if len(successful_results) >= 2:
        times = [v['execution_time'] for v in successful_results.values()]
        fastest = min(times)
        slowest = max(times)
        improvement = ((slowest - fastest) / slowest) * 100
        
        print("🎯 PERFORMANCE ANALYSIS")
        print("-" * 30)
        print(f"Fastest configuration: {fastest:.1f}s")
        print(f"Slowest configuration: {slowest:.1f}s")
        print(f"Performance improvement: {improvement:.1f}%")
        
        if fastest <= 60:
            print("🏆 Target achieved: Under 60 seconds for 2 jobs!")
        else:
            print("⚠️  Target missed: Still over 60 seconds")
    
    return results

def test_timeout_progression():
    """Test the progressive timeout feature"""
    print("\n🔄 Progressive Timeout Test")
    print("=" * 40)
    
    config = ScraperConfig(
        cloudflare_timeout=15,
        progressive_timeout=True,
        cloudflare_max_retries=4
    )
    
    from new_glassdoor import HumanBehavior, ScraperLogger
    
    human_behavior = HumanBehavior(ScraperLogger(), config)
    
    print("Progressive timeout sequence:")
    for attempt in range(1, 5):
        timeout = human_behavior.get_progressive_timeout(attempt)
        print(f"  Attempt {attempt}: {timeout}s timeout")

def main():
    """Main test function"""
    print("🛡️  Enhanced Glassdoor Scraper - Cloudflare Performance Test")
    print("This test measures the performance improvements in Cloudflare bypass")
    print()
    
    # Test progressive timeout
    test_timeout_progression()
    
    # Test performance
    results = test_cloudflare_performance()
    
    print("\n💡 OPTIMIZATION RECOMMENDATIONS")
    print("=" * 60)
    print("Based on test results:")
    print()
    
    successful_configs = [k for k, v in results.items() if v['status'] == 'SUCCESS']
    
    if successful_configs:
        fastest_config = min(successful_configs, 
                           key=lambda k: results[k]['execution_time'])
        
        print(f"🏆 Best performing configuration: {fastest_config}")
        print(f"   Execution time: {results[fastest_config]['execution_time']:.1f}s")
        print(f"   Success rate: {results[fastest_config]['success_rate']}")
        
        print("\n🔧 Recommended settings for production:")
        print("```python")
        print("config = ScraperConfig(")
        print("    driver_mode='undetected',")
        print("    cloudflare_timeout=15,")
        print("    progressive_timeout=True,")
        print("    fast_challenge_detection=True,")
        print("    auto_fallback_drivers=True,")
        print("    use_ultra_fast_forms=True,")
        print(")")
        print("```")
    else:
        print("❌ No configurations succeeded. Check your setup:")
        print("  • Ensure Chrome/ChromeDriver is properly installed")
        print("  • Check network connectivity")
        print("  • Verify anti-detection dependencies are installed")
    
    print("\n📈 Expected Performance Improvements:")
    print("  • Cloudflare timeout: 45s → 15s (67% faster)")
    print("  • Challenge detection: 2s → 0.5s intervals (75% faster)")
    print("  • Form filling: 30-60s → <5s (90% faster)")
    print("  • Total scraping: 4+ minutes → <60s (80% faster)")

if __name__ == "__main__":
    main()
