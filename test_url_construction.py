#!/usr/bin/env python3
"""
Test script to verify URL construction logic for foundit.in job pages
"""

import re
import hashlib
import uuid
from typing import Optional

def create_url_slug(text: str) -> str:
    """Create URL-friendly slug from text"""
    if not text:
        return ""

    # Convert to lowercase
    slug = text.lower()

    # Replace spaces and special characters with hyphens
    slug = re.sub(r'[^\w\s-]', '', slug)  # Remove special chars except spaces and hyphens
    slug = re.sub(r'[-\s]+', '-', slug)   # Replace spaces and multiple hyphens with single hyphen

    # Remove leading/trailing hyphens
    slug = slug.strip('-')

    # Limit length to reasonable size
    if len(slug) > 50:
        slug = slug[:50].rstrip('-')

    return slug

def create_enhanced_location_slug(location: str) -> str:
    """Create enhanced location slug that matches foundit.in format"""
    if not location:
        return "india"

    # Common location mappings for Indian cities
    location_mappings = {
        'hyderabad': 'hyderabad-secunderabad-telangana',
        'bangalore': 'bengaluru-bangalore-karnataka',
        'bengaluru': 'bengaluru-bangalore-karnataka',
        'mumbai': 'mumbai-maharashtra',
        'delhi': 'delhi-new-delhi',
        'chennai': 'chennai-tamil-nadu',
        'pune': 'pune-maharashtra',
        'kolkata': 'kolkata-west-bengal',
        'gurgaon': 'gurgaon-haryana',
        'noida': 'noida-uttar-pradesh'
    }

    # Clean and normalize the location
    location_clean = location.lower().strip()

    # Check if we have a specific mapping
    if location_clean in location_mappings:
        return location_mappings[location_clean]

    # If no specific mapping, create a basic slug
    return create_url_slug(location)

def generate_pseudo_job_id(job_title: str, company_name: str) -> str:
    """Generate a pseudo job ID based on job data"""
    job_string = f"{job_title}-{company_name}".lower()
    hash_object = hashlib.md5(job_string.encode())
    pseudo_id = str(int(hash_object.hexdigest()[:8], 16))
    
    # Ensure it's at least 8 digits
    while len(pseudo_id) < 8:
        pseudo_id = "1" + pseudo_id
    
    return pseudo_id

def construct_job_url(job_title: str, company_name: str, location: str, job_id: str) -> str:
    """Construct foundit.in job URL from components"""
    try:
        # Create slugs for each component
        title_slug = create_url_slug(job_title)
        company_slug = create_url_slug(company_name)
        location_slug = create_enhanced_location_slug(location)
        
        # Construct the URL path
        url_path = f"{title_slug}-{company_slug}-{location_slug}-{job_id}"
        
        # Generate sample search parameters
        search_id = str(uuid.uuid4())
        child_search_id = str(uuid.uuid4())
        search_params = f"?searchId={search_id}&child_search_id={child_search_id}"
        
        # Construct final URL
        job_url = f"https://www.foundit.in/job/{url_path}{search_params}"
        
        return job_url
        
    except Exception as e:
        print(f"Error constructing job URL: {e}")
        return None

def validate_constructed_url(url: str) -> bool:
    """Validate if the constructed URL follows the correct foundit.in pattern"""
    try:
        if not url:
            return False
        
        # Pattern for foundit.in job URLs
        pattern = r'^https://www\.foundit\.in/job/[a-z0-9-]+-[a-z0-9-]+-[a-z0-9-]+-[a-z0-9_]+(\?.*)?$'
        
        return bool(re.match(pattern, url))
        
    except Exception as e:
        print(f"Error validating URL: {e}")
        return False

def test_url_construction():
    """Test URL construction with sample job data from the HTML files"""
    
    # Sample job data extracted from raw.html
    test_jobs = [
        {
            "title": "Subcon demand For Apple project Data scientist",
            "company": "Fusion Plus Solutions",
            "location": "Hyderabad",
            "expected_pattern": "subcon-demand-for-apple-project-data-scientist-fusion-plus-solutions-hyderabad-secunderabad-telangana"
        },
        {
            "title": "Data Scientist gTech Ads Solutions",
            "company": "Google Inc",
            "location": "Hyderabad",
            "expected_pattern": "data-scientist-gtech-ads-solutions-google-inc-hyderabad-secunderabad-telangana"
        },
        {
            "title": "Data Scientist AI Solutions For Electrification",
            "company": "Siemens",
            "location": "Hyderabad",
            "expected_pattern": "data-scientist-ai-solutions-for-electrification-siemens-hyderabad-secunderabad-telangana"
        },
        {
            "title": "Data Scientist ISS",
            "company": "Amazon Development Centre (India) Private Limited",
            "location": "Hyderabad",
            "expected_pattern": "data-scientist-iss-amazon-development-centre-india-private-limited-hyderabad-secunderabad-telangana"
        }
    ]
    
    print("=== Testing URL Construction Logic ===\n")
    
    for i, job in enumerate(test_jobs, 1):
        print(f"Test {i}: {job['title']}")
        print(f"Company: {job['company']}")
        print(f"Location: {job['location']}")
        
        # Generate pseudo job ID
        job_id = generate_pseudo_job_id(job['title'], job['company'])
        print(f"Generated Job ID: {job_id}")
        
        # Create slugs
        title_slug = create_url_slug(job['title'])
        company_slug = create_url_slug(job['company'])
        location_slug = create_enhanced_location_slug(job['location'])
        
        print(f"Title Slug: '{title_slug}'")
        print(f"Company Slug: '{company_slug}'")
        print(f"Location Slug: '{location_slug}'")
        
        # Construct URL
        constructed_url = construct_job_url(job['title'], job['company'], job['location'], job_id)
        print(f"Constructed URL: {constructed_url}")
        
        # Validate URL
        is_valid = validate_constructed_url(constructed_url)
        print(f"URL Valid: {is_valid}")
        
        # Check if the pattern matches expected
        expected_path = f"{job['expected_pattern']}-{job_id}"
        if expected_path in constructed_url:
            print("✅ URL pattern matches expected format")
        else:
            print("❌ URL pattern does not match expected format")
            print(f"Expected pattern: {expected_path}")
        
        print("-" * 80)
    
    # Test with known working URL format
    print("\n=== Testing Against Known Working URL ===")
    known_url = "https://www.foundit.in/job/subcon-demand-for-apple-project-data-scientist-fusion-plus-solutions-hyderabad-secunderabad-telangana-35536382?searchId=c3a1249c-b73e-4532-8915-d6017be8bd0f&child_search_id=670e8c89-097f-4e07-a4ca-1e55deb8506d"
    print(f"Known working URL: {known_url}")
    print(f"Validation result: {validate_constructed_url(known_url)}")
    
    # Extract components from known URL
    url_pattern = r'https://www\.foundit\.in/job/(.+)-(\d+)\?'
    match = re.search(url_pattern, known_url)
    if match:
        path_components = match.group(1)
        job_id = match.group(2)
        print(f"Extracted path: {path_components}")
        print(f"Extracted job ID: {job_id}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_url_construction()
