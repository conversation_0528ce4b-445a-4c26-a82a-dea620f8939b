#!/usr/bin/env python3
"""
Job Portal ngrok Setup Script
Starts both frontend and backend services and creates an ngrok tunnel
"""

import subprocess
import time
import sys
import os
import signal
import requests
from pyngrok import ngrok
import threading
from pathlib import Path

class JobPortalLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.tunnel = None
        self.running = True
        
    def cleanup(self, signum=None, frame=None):
        """Clean up all processes and tunnels"""
        print("\n🛑 Shutting down services...")
        self.running = False
        
        if self.tunnel:
            try:
                ngrok.disconnect(self.tunnel.public_url)
                print("✅ ngrok tunnel closed")
            except:
                pass
                
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend stopped")
            except:
                try:
                    self.backend_process.kill()
                except:
                    pass
                    
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend stopped")
            except:
                try:
                    self.frontend_process.kill()
                except:
                    pass
        
        try:
            ngrok.kill()
        except:
            pass
            
        print("🎉 All services stopped successfully!")
        sys.exit(0)
    
    def start_backend(self):
        """Start the FastAPI backend"""
        print("🔧 Starting FastAPI backend...")
        try:
            self.backend_process = subprocess.Popen(
                ["python", "-m", "uvicorn", "main:app", "--reload", "--port", "8000"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            return True
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self):
        """Start the Next.js frontend"""
        print("🎨 Starting Next.js frontend...")
        try:
            frontend_dir = Path("web-portal")
            if not frontend_dir.exists():
                print("❌ web-portal directory not found")
                return False
                
            # Set environment variable for API base URL
            env = os.environ.copy()
            env["NEXT_PUBLIC_API_BASE_URL"] = ""
            
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            return True
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def wait_for_service(self, url, service_name, max_attempts=30):
        """Wait for a service to be ready"""
        print(f"⏳ Waiting for {service_name} to be ready...")
        for attempt in range(max_attempts):
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"✅ {service_name} is ready!")
                    return True
            except:
                pass
            time.sleep(2)
        
        print(f"❌ {service_name} failed to start within {max_attempts * 2} seconds")
        return False
    
    def create_nginx_config(self):
        """Create a simple nginx configuration for routing"""
        config = """
events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server 127.0.0.1:3000;
    }
    
    upstream backend {
        server 127.0.0.1:8000;
    }
    
    server {
        listen 8080;
        server_name localhost;
        
        client_max_body_size 10M;
        
        # Backend routes
        location ~ ^/(health|docs|redoc|openapi\.json|foundit|glassdoor|simplyhired|ziprecruiter|linkedin|naukri|scrape-linkedin|scrape-indeed)/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }
        
        location /health {
            proxy_pass http://backend/health;
        }
        
        # Frontend routes
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_cache_bypass $http_upgrade;
        }
        
        # Next.js hot reload
        location /_next/webpack-hmr {
            proxy_pass http://frontend/_next/webpack-hmr;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
"""
        with open("nginx-auto.conf", "w") as f:
            f.write(config)
        return "nginx-auto.conf"
    
    def start_nginx(self):
        """Start nginx proxy if available"""
        try:
            # Check if nginx is available
            subprocess.run(["nginx", "-v"], capture_output=True, check=True)
            
            config_file = self.create_nginx_config()
            print("🌐 Starting nginx proxy...")
            
            self.nginx_process = subprocess.Popen(
                ["nginx", "-c", os.path.abspath(config_file), "-p", os.getcwd()],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            time.sleep(2)
            
            # Test if nginx is working
            try:
                response = requests.get("http://localhost:8080/health", timeout=5)
                if response.status_code == 200:
                    print("✅ nginx proxy started successfully!")
                    return 8080
            except:
                pass
                
        except:
            pass
        
        print("⚠️  nginx not available or failed to start. Using direct backend access.")
        return 8000
    
    def run(self):
        """Main execution function"""
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.cleanup)
        signal.signal(signal.SIGTERM, self.cleanup)
        
        print("🚀 Starting Job Portal with ngrok...")
        
        # Start backend
        if not self.start_backend():
            return
        
        # Start frontend
        if not self.start_frontend():
            self.cleanup()
            return
        
        # Wait for services to be ready
        if not self.wait_for_service("http://localhost:8000/health", "Backend"):
            self.cleanup()
            return
            
        if not self.wait_for_service("http://localhost:3000", "Frontend"):
            self.cleanup()
            return
        
        # Try to start nginx proxy
        tunnel_port = self.start_nginx()
        
        # Create ngrok tunnel
        try:
            print(f"🌐 Creating ngrok tunnel on port {tunnel_port}...")
            self.tunnel = ngrok.connect(tunnel_port)
            public_url = self.tunnel.public_url
            
            print("\n🎉 Setup complete!")
            print("=" * 60)
            print(f"📱 Public URL:     {public_url}")
            print(f"🏠 Local URL:      http://localhost:{tunnel_port}")
            print()
            print("🔗 API Endpoints:")
            print(f"   📊 Health:      {public_url}/health")
            print(f"   📚 API Docs:    {public_url}/docs")
            print(f"   🏠 Frontend:    {public_url}/")
            print()
            print("🔗 Direct Local Access:")
            print("   🎨 Frontend:    http://localhost:3000")
            print("   🔧 Backend:     http://localhost:8000")
            print("   📚 API Docs:    http://localhost:8000/docs")
            print()
            print("📊 Monitoring:")
            print("   🌐 ngrok Web Interface: http://localhost:4040")
            print()
            print("🛑 Press Ctrl+C to stop all services")
            print("=" * 60)
            
            # Keep running
            while self.running:
                time.sleep(1)
                
        except Exception as e:
            print(f"❌ Failed to create ngrok tunnel: {e}")
            print("   Make sure you have ngrok installed and configured")
            self.cleanup()

if __name__ == "__main__":
    launcher = JobPortalLauncher()
    launcher.run()
