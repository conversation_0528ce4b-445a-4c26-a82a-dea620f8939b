#!/bin/bash

echo "🚀 Starting Job Portal with Docker and ngrok..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Please install ngrok first."
    echo "   Visit: https://ngrok.com/download"
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ Services are not ready yet. Checking logs..."
    docker-compose logs
    exit 1
fi

echo "✅ Services are running!"
echo "   - Frontend + Backend: http://localhost:8080"
echo "   - Backend API docs: http://localhost:8080/docs"
echo "   - Health check: http://localhost:8080/health"

# Start ngrok tunnel
echo "🌐 Starting ngrok tunnel..."
ngrok http 8080 --log=stdout &
NGROK_PID=$!

# Wait a moment for ngrok to start
sleep 3

# Get ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data['tunnels'][0]['public_url'])
except:
    print('Error getting ngrok URL')
")

if [[ $NGROK_URL == *"ngrok"* ]]; then
    echo "🎉 Setup complete!"
    echo ""
    echo "📱 Access your application:"
    echo "   Public URL: $NGROK_URL"
    echo "   Local URL:  http://localhost:8080"
    echo ""
    echo "🔗 API Endpoints:"
    echo "   Health:     $NGROK_URL/health"
    echo "   API Docs:   $NGROK_URL/docs"
    echo "   Frontend:   $NGROK_URL/"
    echo ""
    echo "📊 Monitoring:"
    echo "   ngrok Web Interface: http://localhost:4040"
    echo "   Docker logs: docker-compose logs -f"
    echo ""
    echo "🛑 To stop everything:"
    echo "   Press Ctrl+C, then run: docker-compose down"
else
    echo "❌ Failed to get ngrok URL. Check if ngrok is running properly."
fi

# Keep script running
echo "Press Ctrl+C to stop all services..."
trap "echo '🛑 Stopping services...'; docker-compose down; kill $NGROK_PID 2>/dev/null; exit" INT
wait
